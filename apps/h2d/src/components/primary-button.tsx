import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ventHandler, PropsWithChildren } from 'react';
import clsx from 'clsx';
import Style from './primary-button.module.less';
import { Button, type ButtonProps } from 'tdesign-react';

interface PrimaryButtonProps extends Omit<ButtonProps, 'size'> {
  className?: string;
  size?: 'default' | 'large';
}

export default function PrimaryButton(props: PrimaryButtonProps) {
  const { children, size = 'default', ...restProps } = props;
  return (
    <Button
      {...restProps}
      className={clsx(Style.button, props.className, {
        [Style.disabled]: props.disabled,
        [Style.big]: size === 'large',
      })}
    >
      {children}
    </Button>
  );
}
