import { memo, MouseEventHandler, useEffect, useState } from 'react';
import { messages } from '@worker/index';
import { MessageTypes } from '@ai-assitant/ai-core';

import Style from './resize.module.less';
import SvgIcon from './svgIcon';

/**
 * 修改大小组件
 * @returns
 */
const Resize = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const [dragging, setDragging] = useState(false);

  const [diffY, setDiffY] = useState(0);

  const handleMouseDown: MouseEventHandler<HTMLDivElement> = (event) => {
    setDragging(true);
    setDiffY(document.body.scrollHeight - event.pageY);
    setPosition({
      x: event.pageX - position.x,
      y: event.pageY - position.y,
    });
  };

  const handleMouseMove = (event: any) => {
    if (dragging) {
      messages[MessageTypes.RESIZE_TO].send({
        width: 480,
        height: event.pageY + diffY,
      });

      // 更新元素位置
      setPosition({
        x: event.pageX - position.x,
        y: event.pageY - position.y,
      });
    }
  };

  const handleMouseUp = () => {
    setDragging(false);
  };

  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [position, diffY]);

  return (
    <div onMouseDown={handleMouseDown} className={Style.resize}>
      <SvgIcon name="resize-left" iconStyle={{ width: '24px', height: '24px' }} />
    </div>
  );
};

export default memo(Resize);
