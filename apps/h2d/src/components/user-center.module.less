@import '../styles/vars.less';

.userMask {
  position: absolute;
  z-index: 5;
  top: 0;
  left: 0;

  width: 100%;
  height: 100%;

  background-color: rgba(0, 0, 0, 0.3);

  backdrop-filter: blur(14.7px);
}

.userCenter {
  position: absolute;
  z-index: 6;
  right: 10px;
  bottom: 47px;

  display: flex;
  flex-direction: column;

  box-sizing: border-box;
  width: 320px;
  height: 290px;

  border: solid 1px @border-color-1;
  border-radius: 8px;
  background-color: @bg-color-1;

  &Header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    padding: 16px 16px 0 16px;

    color: #ffffff;

    font-weight: 500;
  }

  &Content {
    display: flex;
    align-items: center;
    flex: 1;
    flex-direction: column;
    justify-content: flex-start;

    .userName {
      margin-top: 8px;
      margin-bottom: 16px;

      color: #ffffff;

      font-size: 14px;
      line-height: 22px;
    }
  }

  &Btn {
    display: flex;
    align-items: center;
    justify-content: space-between;

    box-sizing: border-box;
    width: 280px;
    height: 54px;
    margin-bottom: 12px;
    padding: 16px;

    cursor: pointer;

    color: #ffffff;
    border: solid 1px @border-color-2;
    border-radius: 8px;
    background-color: #1b1b1b;

    font-weight: 500;
  }

  &Link {
    cursor: pointer;

    color: @text-color-1;

    font-size: 12px;
    line-height: 20px;

    &:hover {
      color: #ffffff;
    }
  }

  &Close {
    cursor: pointer;
  }
}
