import { useMemo, useState } from 'react';
import { Dropdown, DropdownOption } from 'tdesign-react';
import { useTranslation } from 'react-i18next';
import { locales } from '@ui/locale';
import imgYuanBao from '@ui/assets/engine-yuanbao.svg';
import Style from './index.module.less';

export type AIModel = {
  key: string;
  name: string;
  englishName: string;
  icon: string;
};
export const DEFAULT_AI_MODEL = 'hunyuan-vision';
export const AI_MODELS: AIModel[] = [
  {
    key: 'hunyuan-vision',
    name: '腾讯元宝',
    englishName: 'Tencent Hunyuan',
    icon: imgYuanBao,
  },
  {
    key: 'gpt-4o',
    name: 'GPT-4o',
    englishName: 'GPT-4o',
    icon: imgYuanBao,
  },
  {
    key: 'gemini-1.5-pro',
    name: 'Gemini-1.5',
    englishName: 'Gemini-1.5',
    icon: imgYuanBao,
  },
];

export interface ModelSelect {
  models?: AIModel[];
  value?: string;
  onSelect?: (model: string) => void;
}

/**
 * AI 模型选择器
 */
export default function ModelSelect(props: ModelSelect) {
  const { i18n } = useTranslation();
  const { models = AI_MODELS, value, onSelect } = props;

  const [internalValue, setInternalValue] = useState(value ?? DEFAULT_AI_MODEL);

  const modelOptions = useMemo(() => {
    return models.map((v) => ({
      content: i18n.language === locales[0] ? v.englishName : v.name,
      value: v.key,
      prefixIcon: <img src={v.icon} />,
    }));
  }, [i18n.language]);

  const onChangeEngine = (item: DropdownOption) => {
    const selectedModel = item.value as string;
    if (value === undefined) {
      setInternalValue(selectedModel);
    }
    onSelect?.(selectedModel);
  };

  const realValue = value ?? internalValue;

  const curModelOption = useMemo(() => {
    return models.find((it) => it.key === realValue)!;
  }, [realValue]);

  return (
    <Dropdown
      options={modelOptions}
      maxColumnWidth={200}
      onClick={onChangeEngine}
      trigger="click"
      popupProps={{
        overlayInnerClassName: Style.inputEngineDropdown,
      }}
    >
      <div className={Style.inputShow}>
        <img src={curModelOption.icon} />
        <span>{i18n.language === locales[0] ? curModelOption.englishName : curModelOption.name}</span>
      </div>
    </Dropdown>
  );
}
