import React, {
  useEffect,
  useRef,
  useState,
  memo,
  forwardRef,
  useImperativeHandle,
  PropsWithChildren,
  useMemo,
} from 'react';
import { ArrowDownIcon } from 'tdesign-icons-react';
import BScroll, { BScrollInstance } from 'better-scroll';
import { useDebounceEffect, useSize } from 'ahooks';

import Style from './scroll-list.module.less';

/**
 * 滚动列表组件属性
 */
type ScrollListProps = PropsWithChildren<{
  // 是否是flex布局
  flex?: boolean;
  // 设置高度，默认是100%
  height?: number;
  // 内边距
  padding?: string;
  // 是否自动滚动到最底部
  autoToBottom?: boolean;
  // 滚动轴方向
  direction?: 'vertical' | 'horizontal';
  // 显示滚到底部按钮
  showToBottom?: boolean;
  preventDefault?: boolean;
}>;

/**
 * 滚动列表组件实例
 */
export interface ScrollListRef extends React.RefObject<unknown> {
  /**
   * 滚动到底部
   * @returns
   */
  scrollToBottom: () => void;
}

/**
 * 滚动列表组件
 */
const ScrollList = forwardRef<ScrollListRef, ScrollListProps>((props, ref) => {
  const { direction, showToBottom, flex, padding } = props;

  // 外层的wrap实例
  const wrapRef = useRef<HTMLDivElement>(null);
  // 内层的 content实例
  const contentRef = useRef<HTMLDivElement>(null);
  //  存储better-scroll的实例
  const scrollObj = useRef<BScrollInstance>();
  // 隐藏滚动到底部按钮
  const [hidden, setHidden] = useState(true);

  //  对象初始化
  useEffect(() => {
    initBScroll();
    return () => {
      // 组件卸载时销毁滚动轴实例
      scrollObj.current?.destroy();
    };
  }, []);

  // 初始化滚动轴
  const initBScroll = () => {
    const bsInstance = new BScroll(wrapRef.current as HTMLDivElement, {
      //probeType 为 3，任何时候都派发 scroll 事件，包括调用 scrollTo 或者触发 momentum 滚动动画
      probetype: 3,
      //  可以使用原生的点击
      click: true,
      //  检测dom变化
      observeDOM: true,
      //  鼠标滚轮设置
      mouseWheel: {
        speed: 20,
        invert: false,
        easeTime: 300,
      },
      //  显示滚动条
      scrollY: direction === 'vertical' || direction === undefined ? true : false,
      scrollX: direction === 'horizontal' ? true : false,
      scrollbar: false,
      bounce: false,
      //  过度动画, 在下载更多的时候滚动条会有个过度动画
      useTransition: true,
      //  下拉刷新
      pullDownRefresh: {
        threshold: 70,
        stop: 0,
      },
      preventDefault: props.preventDefault === undefined ? false : props.preventDefault,
      //  上拉加载更多
      pullUpLoad: {
        threshold: 90,
        stop: 10,
      },
    });
    scrollObj.current = bsInstance;

    scrollObj.current.on('scroll', (pos: any) => {
      setHidden(scrollObj.current?.maxScrollY === scrollObj.current?.y);
    });
  };

  const contentSize = useSize(contentRef);

  const [contentHeight, setContentHeight] = useState(0);

  // 监听content的变化，自动滚动到最底部
  useDebounceEffect(
    () => {
      if (contentSize) {
        setContentHeight(contentSize.height);
      }

      if (scrollObj && contentHeight) {
        if (props.autoToBottom && hidden) {
          scrollObj.current?.scrollTo(0, scrollObj.current?.maxScrollY);
        }
      }
    },
    [contentSize],
    {
      wait: 100,
    },
  );

  /**
   * 滚动到底部
   */
  const scrollToBottom = () => {
    scrollObj.current?.scrollTo(0, scrollObj.current.maxScrollY, 100);
  };

  useImperativeHandle(ref as ScrollListRef, () => ({
    scrollToBottom: () => {
      if (scrollObj.current) {
        scrollObj.current.refresh();
        scrollObj.current?.scrollTo(0, scrollObj.current.maxScrollY);
      }
    },
  }));

  const customClass = useMemo(() => {
    let name = '';

    if (flex) {
      name = Style.scrollFlexList;
    } else {
      name = Style.scrollList;
    }

    if (direction === 'vertical' || direction === undefined) {
      name += ' ' + Style.scrollVertical;
    }

    return name;
  }, [direction, flex]);

  return (
    <div ref={wrapRef} className={customClass}>
      <div ref={contentRef} className={Style.scrollContent} style={{ padding }}>
        {props.children}
      </div>
      {showToBottom && !hidden && (
        <div onClick={scrollToBottom} className={Style.scrollToBottom}>
          <ArrowDownIcon size="24px" style={{ color: 'rgba(255,255,255,0.8)' }} />
        </div>
      )}
    </div>
  );
});

export default memo(ScrollList);
