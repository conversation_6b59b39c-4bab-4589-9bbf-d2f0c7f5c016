import { memo } from 'react';
import { ChevronLeftIcon } from 'tdesign-icons-react';
import { useUserStore } from '@ui/store';
import ScrollList from './scroll-list';
import markdownit from 'markdown-it';

// import MdHelp from '../assets/policy.md';
import Style from './help.module.less';

const md = new markdownit({
  breaks: true,
  linkify: true,
});

/**
 *  帮助组件
 * @returns
 */
const Help = () => {
  const { showHelp, setShowHelp } = useUserStore();

  const handleBack = () => {
    setShowHelp(false);
  };

  return (
    <>
      {showHelp && (
        <div className={Style.help}>
          <div className={Style.helpHeader}>
            <div className={Style.helpBack} onClick={handleBack}>
              <ChevronLeftIcon style={{ marginRight: '5px' }} />
              <span>返回</span>
            </div>
          </div>
          <div className={Style.helpContent}>
            <ScrollList>{/* <MdHelp /> */}</ScrollList>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Help);
