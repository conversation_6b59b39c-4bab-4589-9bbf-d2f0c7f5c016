@import '../styles/vars.less';

.avatar {
  position: relative;

  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  .avatarText {
    display: flex;
    align-items: center;
    justify-content: center;

    user-select: none;

    color: #000000;
    border-radius: 50%;
    background-color: @primary-color;

    font-size: 12px;
    line-height: 16px;
  }

  .avatarImg {
    border-radius: 50%;
  }

  .avatarRole {
    height: 24px;
    margin-top: -10px;
    padding: 0px 7px;

    transform: rotate(10deg);

    color: #000000;
    border-radius: 4px;
    background-color: @primary-color;

    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
  }
}
