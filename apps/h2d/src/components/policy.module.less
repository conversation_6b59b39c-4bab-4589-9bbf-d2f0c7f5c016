@import '../styles/vars.less';

.policy {
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;

  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: flex-start;

  width: 100%;
  height: 100%;

  color: #ffffff;
  background-color: @bg-color-3;

  line-height: 24px;

  &Header {
    display: flex;
    align-items: center;

    width: 100%;
    padding-left: 20px;

    font-size: 14px;
    line-height: 40px;
  }

  &Back {
    display: flex;
    align-items: center;

    cursor: pointer;
  }

  &Content {
    overflow-y: auto;
    flex: 1;

    width: 100%;

    font-size: 12px;

    p {
      text-indent: 2em;
    }
  }

  .policyMarkdown {
    width: 100%;

    text-wrap: wrap;
  }

  &Title {
    margin-bottom: 16px;

    color: #ffffff;

    font-weight: 500;
  }
}
