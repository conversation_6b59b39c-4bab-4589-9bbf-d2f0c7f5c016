import { memo, PropsWithChildren, useMemo } from 'react';
import { useUserStore } from '@ui/store';

import Style from './avatar.module.less';

/**
 * 用户头像组件属性
 */
export type AvatarProps = PropsWithChildren<{
  /**
   * 大小
   */
  size: number;
  /**
   * 字体大小
   */
  fontSize: number;
  /**
   * 是否显示角色
   */
  showRole?: boolean;
}>;

/**
 *  用户头像组件
 * @param props
 * @returns
 */
const Avatar = (props: AvatarProps) => {
  const { size, fontSize, showRole } = props;

  const { userInfo } = useUserStore();

  const userName = useMemo(() => {
    return userInfo?.nickname;
  }, [userInfo]);

  const avatar = useMemo(() => {
    if (userInfo && userInfo.avatar) {
      if (userInfo.avatar.startsWith('http')) {
        return userInfo.avatar;
      }

      return '';
    }

    return '';
  }, [userInfo]);

  const shotName = useMemo(() => {
    if (userName?.length > 0) {
      return userName.charAt(0).toUpperCase();
    } else {
      return '';
    }
  }, [userName]);

  const role = useMemo(() => {
    return userInfo?.position || '';
  }, [userInfo]);

  return (
    <div className={Style.avatar}>
      {avatar !== '' && <img className={Style.avatarImg} width={size} height={size} src={avatar} />}
      {avatar === '' && (
        <div
          className={Style.avatarText}
          style={{ width: `${size}px`, height: `${size}px`, fontSize: `${fontSize}px` }}
        >
          <span>{shotName}</span>
        </div>
      )}
      {showRole && role && <div className={Style.avatarRole}>{role}</div>}
    </div>
  );
};

export default memo(Avatar);
