import React, { memo, PropsWithChildren, ReactNode, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Popup, Progress, Textarea } from 'tdesign-react';
import { ThumbDown2Icon, ThumbUp2Icon } from 'tdesign-icons-react';
import { Nullable } from '@ai-assitant/ai-core';

import ScrollList from '../scroll-list';
import SvgIcon from '@ui/components/svgIcon';
import { useStageStore } from '@ui/store';
import { StageEvent, StageTask, StageStep, StageState } from './stageManager';

import Style from './index.module.less';

import imgRun from '@ui/assets/stage-run.gif';
import imgEnd from '@ui/assets/stage-end.gif';
import track from '@ui/utils/track';
import SubmitCard from '@ui/components/submit-card';
import UnlikeClick from '@ui/assets/stage-unlike-click.svg';

/**
 * 进度展示组件属性
 */
export type StageProps = PropsWithChildren<{
  /**
   * 信息展示
   */
  info: JSX.Element;
  /**
   * 任务展示
   */
  task: JSX.Element;
}>;

/**
 * 进度展示组件
 * @returns
 */
const Stage = (props: StageProps) => {
  const { children, info, task } = props;
  const { t } = useTranslation();

  const { showStage, stageManager, setShowStage } = useStageStore();

  /**
   * 当前步骤
   */
  const [currentStep, setCurrentStep] = useState<Nullable<StageStep>>(null);

  /**
   * 运行任务
   */
  const [tasks, setTasks] = useState<StageTask[]>([]);

  /**
   * 运行步骤
   */
  const [steps, setSteps] = useState<StageStep[]>([]);

  /**
   * 进度状态
   */
  const [stageState, setStageState] = useState<StageState>('Prepare');

  const [isUpActive, setIsUpActive] = useState(false);
  const [isDownActive, setIsDownActive] = useState(false);
  /**
   * 弹窗内容显隐
   */

  const [popupVisible, setPopupVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const handleCancelSendMessage = () => {
    if (props.info.props.type !== 'html') {
      track.send('result.bad.click', {
        eventLabel: `${props.info.props.type} - ${inputValue}`,
        eventValue: props.info.props?.loadingPage,
      });
    }
    setPopupVisible(false);
    setTimeout(() => {
      handleBack();
    }, 500);
  };
  /**
   * 点踩
   */
  const handleDownClick = () => {
    if (isDownActive) {
      setIsDownActive(false);
      setPopupVisible(false);
      return;
    }
    setIsDownActive(true);
    setPopupVisible(true);
  };
  /**
   * 点赞
   */
  const handleUpClick = () => {
    if (isUpActive) {
      setIsUpActive(false);
      return;
    }
    setIsUpActive(true);
    track.send('result.good.click', { eventLabel: props.info.props.type, eventValue: props.info.props?.loadingPage });
    setTimeout(() => {
      handleBack();
    }, 500);
  };

  useEffect(() => {
    if (stageManager) {
      stageManager.on((data: StageEvent) => {
        setStageState('Running');
        if (data.type === 'STEP' && data.step) {
          setCurrentStep(data.step);
          setSteps(stageManager.Steps);
        }
        if (data.type === 'TASK' && data.task) {
          setTasks(stageManager.Tasks);
        }

        if (data.type === 'STAGE') {
          setStageState(stageManager.State);
          setSteps([]);
        }
      });
    }

    return () => {
      if (stageManager) {
        stageManager.off();
      }
    };
  }, [showStage]);

  useEffect(() => {
    if (task.type === React.Fragment) {
      setStageState('Success');
    }
  }, []);

  useEffect(() => {
    if (stageManager?.isFinish) {
      setCurrentStep(null);
    }
  }, [currentStep]);

  const handleBack = () => {
    setShowStage(false);

    clear();
  };

  const handleStop = () => {
    stageManager?.stop();

    setShowStage(false);
    clear();
  };

  const clear = () => {
    setInputValue('');
    stageManager?.clear();
    setTasks([]);
    setSteps([]);
    setIsUpActive(false);
    setIsDownActive(false);
    setCurrentStep(null);
  };

  const renderTask = (taskItem: StageTask): JSX.Element => {
    return React.cloneElement(task, { task: taskItem });
  };

  return (
    <>
      {showStage && (
        <div className={Style.stagePage}>
          <div className={Style.stageState}>
            {(stageState === 'Prepare' || stageState === 'Running') && <img src={imgRun} />}
            {(stageState === 'Success' || stageState === 'Failed') && <img src={imgEnd} />}
          </div>
          {steps.length > 0 && <div className={Style.stageInfo}>{info}</div>}
          <ScrollList flex={true} autoToBottom={true} padding={'0px'} direction={'vertical'}>
            {tasks
              .filter((it) => it.state === 'Success' || it.state === 'Failed')
              .map((it) => {
                return (
                  <div className={Style.stageStepItem} key={it.key}>
                    {(it.state === 'Success' || it.state === 'Failed') && renderTask(it)}
                  </div>
                );
              })}
          </ScrollList>
          {steps.length > 0 && (
            <div className={Style.stageStep}>
              <div className={Style.stageStepItems}>
                {/* 运行步骤 */}
                {steps.map((it) => {
                  return (
                    <div className={Style.stageStepItem} key={it.key}>
                      <div className={Style.stageStepItemBar}>
                        <div className={Style.stageStepItemDot}></div>
                        <div className={Style.stageStepItemName}>{it.name}</div>
                        {it.progress === 100 && (
                          <SvgIcon name="stage-finish" iconStyle={{ width: '16px', height: '16px' }} />
                        )}
                      </div>
                      {currentStep?.key === it.key && (
                        <div className={Style.stageStepItemProgress}>
                          <Progress
                            strokeWidth={8}
                            color={'#61F29F'}
                            trackColor={'#E7ECF6'}
                            percentage={currentStep.progress}
                          ></Progress>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}
          <div className={Style.stageOper}>
            {stageState === 'Running' && (
              <div className={Style.stageBtnStop} onClick={handleStop}>
                {t('stage.stop')}
              </div>
            )}
          </div>
          <div className={Style.stageFeedBack}>
            {stageState !== 'Running' && (
              <div className={Style.stageBtn} onClick={handleBack}>
                {t('stage.back')}
              </div>
            )}
            <Popup
              overlayClassName={Style.stagePopup}
              overlayInnerClassName={Style.stagePopupInner}
              content={
                <SubmitCard
                  title={t('Dialog.Title')}
                  input={
                    <Textarea
                      placeholder={t('Dialog.Placeholder')}
                      rows={3}
                      value={inputValue}
                      onChange={(val) => setInputValue(val)}
                    />
                  }
                  confirmBtn={
                    <Button
                      className={Style.btnSend}
                      shape="rectangle"
                      variant="outline"
                      onClick={handleCancelSendMessage}
                    >
                      {t('Dialog.ConfirmBtn')}
                    </Button>
                  }
                  cancelBtn={
                    <Button
                      className={Style.btnCancel}
                      shape="rectangle"
                      variant="outline"
                      onClick={handleCancelSendMessage}
                    >
                      {t('Dialog.CancelBtn')}
                    </Button>
                  }
                />
              }
              trigger="click"
              placement="top"
              visible={popupVisible}
            >
              {(stageState === 'Success' || stageState === 'Failed') && (
                <>
                  {!popupVisible && <div className={Style.stageQuestion}>{t('stage.question')}</div>}
                  <div className={Style.stageComment}>
                    <div
                      className={`${Style.stageCommentItem} ${Style.stageCommentLike} ${isUpActive ? Style.stageCommentLikeActive : ''}`}
                    >
                      <ThumbUp2Icon
                        size={'24px'}
                        style={{ color: '#FFFFFF' }}
                        onClick={() => {
                          handleUpClick();
                        }}
                      />
                    </div>
                    <div
                      className={`${Style.stageCommentItem} ${Style.stageCommentUnlike} ${isDownActive ? Style.stageCommentUnlikeActive : ''}`}
                    >
                      {!isDownActive && (
                        <ThumbDown2Icon
                          size={'24px'}
                          style={{ color: '#FFFFFF' }}
                          onClick={() => {
                            handleDownClick();
                          }}
                        />
                      )}
                      {!!isDownActive && (
                        <img
                          src={UnlikeClick}
                          onClick={() => {
                            handleDownClick();
                          }}
                        />
                      )}
                    </div>
                  </div>
                </>
              )}
            </Popup>
          </div>
          {children}
        </div>
      )}
    </>
  );
};

export default memo(Stage);
