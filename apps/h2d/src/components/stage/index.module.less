@import '../../styles/vars.less';

.stagePage {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  height: 97vh;
  gap: 16px;

  .stageFeedBack {
    position: fixed;
    bottom: 10vh; // 使用视口单位代替百分比
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 16px;
    z-index: 1000;
    pointer-events: none; // 允许点击穿透到下方元素

    > * {
      pointer-events: auto; // 恢复子元素点击事件
    }
  }

  .stageState {
    display: flex;
    align-items: center;
    flex-direction: column;
    position: absolute;
    top: 5%;

    img {
      height: 250px;
    }
  }

  .stageInfo {
    display: flex;
    flex-direction: column;
    margin-bottom: 80px;
    width: 100%;
  }

  .stageStep {
    display: flex;
    align-items: center;
    flex-direction: column;

    box-sizing: border-box;
    width: 100%;
    padding: 16px;
    margin-bottom: -160px;

    border: solid 1px @border-color-1;
    border-radius: 8px;
    background-color: @bg-color-1;

    font-size: 12px;

    &Items {
      display: flex;
      align-items: flex-start;
      flex-direction: column;

      gap: 8px;
    }

    &Item {
      display: flex;
      align-items: flex-start;
      flex-direction: column;

      width: 100%;

      gap: 5px;

      &Bar {
        display: flex;
        align-items: center;
        flex-direction: row;

        gap: 8px;
      }

      &Dot {
        width: 6px;
        height: 6px;

        border-radius: 50%;
        background-color: @primary-color;
      }

      &Name {
        color: #ffffff;

        font-size: 12px;
        line-height: 20px;
      }

      &Progress {
        width: 200px;
      }

      :global(.t-progress__info) {
        color: #ffffff;
      }
    }
  }

  .stageOper {
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  .stageBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 80px;
    height: 32px;
    cursor: pointer;
    color: #ffffff;
    border: solid 1px @border-color-3;
    border-radius: 4px;
    background-color: transparent;
    font-size: 14px;
    font-weight: 500;
  }

  .stageBtnStop {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 80px;
    height: 32px;
    cursor: pointer;
    color: #ffffff;
    border: solid 1px @border-color-3;
    border-radius: 4px;
    background-color: transparent;
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    bottom: 12%;
  }

  .stageQuestion {
    margin-top: 30px;
    margin-bottom: 20px;

    color: @text-color-1;

    font-size: 14px;
  }

  .stageComment {
    display: flex;
    align-items: center;
    justify-content: center;

    gap: 24px;

    &Item {
      display: flex;
      align-items: center;
      justify-content: center;

      box-sizing: border-box;
      width: 42px;
      height: 42px;

      cursor: pointer;

      border: solid 1px #ffffff;
      border-radius: 50%;
    }

    &Like {
      &:hover, &Active {
        border: none;
        background-color: @primary-color;
      }
    }

    &Unlike {
      &:hover, &Active {
        border: none;
        color: black;
        background-color: #ff5959;
      }
    }
  }
}

.stagePopup {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 0px;
}

.stagePopupInner {
  background: none;
  box-shadow: none;
}

.btnSend {
  background-color: @primary-color;
  border: solid 1px @border-color-1;
}

.btnSend:hover {
  background-color: @primary-color;
  border: solid 1px @border-color-1;
  color: rgb(27, 27, 27);
}

.btnCancel {
  background-color: rgba(0, 0, 0, 0);
  border: solid 1px @border-color-1;
  color: @text-color-2;
}

.btnCancel:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border: solid 1px @border-color-1;
  color: @text-color-2;
}

