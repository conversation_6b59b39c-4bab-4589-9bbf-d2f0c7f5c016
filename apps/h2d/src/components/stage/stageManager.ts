import { Nullable } from '@ai-assitant/ai-core';
import { cloneDeep } from 'lodash-es';
import { interval, Subject, Subscription } from 'rxjs';

/**
 * 进度管理配置项
 */
export type StageOptions = {
  /**
   * 任务集合
   */
  tasks: string[];
  /**
   * 任务步骤
   */
  steps: string[];
};

/**
 * 进度事件
 */
export type StageEvent = {
  /**
   * 事件类型
   */
  type: 'TASK' | 'STEP' | 'STAGE' | 'ERROR';
  /**
   * 步骤
   */
  step?: StageStep;
  /**
   * 任务
   */
  task?: StageTask;
  /**
   * 错误原因
   */
  errorReason?: string;
};

/**
 * 默认配置项
 */
const DEFAULT_OPTIONS = {
  tasks: [],
  steps: [],
};

/**
 * 进度管理
 */
export class StageManager {
  /**
   * 配置选项
   */
  private options: StageOptions = cloneDeep(DEFAULT_OPTIONS);

  /**
   * 步骤集合
   */
  public steps: StageStep[] = [];

  /**
   * 任务集合
   */
  private tasks: StageTask[] = [];

  /**
   * 当前运行步骤
   */
  private runStep: Nullable<StageStep> = null;

  /**
   * 当前运行任务
   */
  private runTask: string = '';

  /**
   * 进度状态
   */
  private state: StageState = 'Prepare';

  /**
   * 通知事件
   */
  private subject = new Subject<StageEvent>();

  /**
   * 通知事件订阅者
   */
  private subscription: Nullable<Subscription> = null;

  /**
   * 模拟进度事件
   */
  private stepSubject = interval(1000);

  /**
   * 模拟进度事件订阅者
   */
  private stepSubscription: Nullable<Subscription> = null;

  /**
   * 停止事件
   */
  private stopSubject = new Subject();

  /**
   * 停止事件订阅者
   */
  private stopSubscription: Nullable<Subscription> = null;

  /**
   * 构造函数
   */
  constructor(options: StageOptions) {
    this.options = options;

    this.initSteps();
    this.initTasks();

    this.state = 'Prepare';
  }

  public get Steps() {
    return this.steps;
  }

  public get Tasks() {
    return this.tasks;
  }

  public get isFinish() {
    return this.runTask === '';
  }

  public get State() {
    return this.state;
  }

  /**
   * 通知事件监听
   * @param listener
   * @returns
   */
  public on(listener: (event: StageEvent) => void): Subscription {
    if (this.subscription === null) {
      this.subscription = this.subject.subscribe((event) => {
        listener(event);
      });
    }

    return this.subscription;
  }

  /**
   * 通知事件释放
   */
  public off() {
    if (this.subscription) {
      this.subscription.unsubscribe();
      this.subscription = null;
    }
  }

  /**
   * 通知事件触发
   * @param rest 多个参数
   * 第一个参数 事件类型【'STEP','ERROR】
   * 类型为【STEP】时,剩余参数依次为 taskName,stepName,progress
   * 类型为【ERROR】时,剩余参数依次为 taskName,errorReason
   *
   */
  public emit(type: 'STEP', taskName: string, stepName: string, progress: number): void;
  public emit(type: 'ERROR', taskName: string, errorReason: string): void;
  public emit(type: 'STEP' | 'ERROR', taskName: string, stepName: string, progress?: number) {
    if (type === 'ERROR') {
      this.errorTask(taskName, stepName);
      return;
    }

    const prevStepData = cloneDeep(this.runStep);
    this.runStep = {
      task: taskName,
      key: this.getStepKey(stepName),
      name: stepName,
      progress: progress ?? 0,
    };

    // 判断是否没有运行任务
    if (this.runTask === '') {
      this.runTask = taskName;
      this.startStep();
    } else if (this.runTask === taskName) {
      if (prevStepData?.name === stepName) {
        this.finishStep();
      } else {
        // 正在运行任务的下一步骤
        this.startStep();
      }
    } else {
      this.initSteps();

      // 下一任务
      this.runTask = taskName;
      this.startStep();
    }
  }

  /**
   * 开始步骤
   */
  private startStep() {
    if (this.runStep) {
      this.subject.next({
        type: 'STEP',
        step: this.runStep,
      });
    }

    this.removeStepSubscription();

    this.stepSubscription = this.stepSubject.subscribe({
      next: (value) => {
        if (this.runStep) {
          if (this.runStep.progress >= 90) {
            this.removeStepSubscription();
            return;
          }

          this.runStep = {
            ...this.runStep,
            progress: this.runStep.progress + 10,
          };
          this.subject.next({
            type: 'STEP',
            step: this.runStep,
          });
        }
      },
    });
  }

  /**
   * 结束步骤
   */
  private finishStep() {
    if (this.runStep) {
      this.removeStepSubscription();

      this.subject.next({
        type: 'STEP',
        step: this.runStep,
      });

      let idx = this.steps.findIndex((it) => it.name === this.runStep?.name);
      this.steps[idx] = cloneDeep(this.runStep);

      // 最后一个步骤
      if (idx === this.steps.length - 1 && this.runStep.progress === 100) {
        this.runStep = null;

        this.finishTask(this.runTask);
      }
    }
  }

  /**
   * 结束任务
   * @param name 任务名称
   */
  private finishTask(name: string) {
    let idx = this.options.tasks?.findIndex((it) => it === name);
    this.tasks[idx] = {
      ...this.tasks[idx],
      state: 'Success',
    };

    this.subject.next({
      type: 'TASK',
      task: this.tasks[idx],
    });

    // 最后一个任务
    if (idx === this.options.tasks?.length - 1) {
      this.state = 'Success';
      this.runTask = '';
      this.steps = [];

      this.subject.next({
        type: 'STAGE',
      });
    }
  }

  /**
   * 任务报错
   * @param name
   * @param message
   */
  private errorTask(name: string, message: string) {
    let idx = this.options.tasks?.findIndex((it) => it === name);
    this.tasks[idx] = {
      ...this.tasks[idx],
      state: 'Failed',
      errorMessage: message,
    };

    this.subject.next({
      type: 'TASK',
      task: this.tasks[idx],
    });

    // 最后一个任务
    if (idx === this.options.tasks?.length - 1) {
      this.state = 'Failed';
      this.runTask = '';
      this.steps = [];

      this.removeStepSubscription();
      this.subject.next({
        type: 'STAGE',
      });
    }
  }

  /**
   * 移除模拟进度订阅者
   */
  private removeStepSubscription() {
    if (this.stepSubscription) {
      this.stepSubscription.unsubscribe();
      this.stepSubscription = null;
    }
  }

  /**
   * 停止进度
   */
  public stop() {
    this.state = 'Stop';
    this.stopSubject.next('');
  }

  public finish() {
    this.state = 'Success';
    this.subject.next({
      type: 'STAGE',
    });
  }

  public onStop(listener: () => void): Subscription {
    if (this.stopSubscription === null) {
      this.stopSubscription = this.stopSubject.subscribe((event) => {
        listener();
      });
    }

    return this.stopSubscription;
  }

  public getStepKey(step: string) {
    return 'step-' + this.options.steps.findIndex((it) => it === step);
  }

  /**
   * 初始化步骤列表
   */
  private initSteps() {
    this.steps = this.options.steps.map((it, idx) => {
      return {
        task: '',
        key: `step-${idx}`,
        name: it,
        progress: 0,
      };
    });
  }

  /**
   * 初始化任务列表
   */
  private initTasks() {
    this.tasks = this.options.tasks.map((it, idx) => {
      return {
        key: `task-${idx}`,
        name: it,
        state: 'Prepare',
      };
    });
  }

  /**
   * 清除所有数据
   */
  public clear() {
    this.options = cloneDeep(DEFAULT_OPTIONS);
    this.state = 'Prepare';
    this.steps = [];
    this.tasks = [];
  }
}

/**
 * 进度状态
 */
export type StageState = 'Prepare' | 'Running' | 'Success' | 'Failed' | 'Stop';

/**
 * 进度任务
 */
export type StageTask = {
  /**
   * 任务key
   */
  key: string;

  /**
   * 任务名称
   */
  name: string;
  /**
   * 任务状态
   */
  state: StageState;
  /**
   * 错误信息
   */
  errorMessage?: string;
};

/**
 * 进度步骤
 */
export type StageStep = {
  /**
   * 任务键值
   */
  task: string;
  /**
   * 步骤key
   */
  key: string;
  /**
   * 步骤名称
   */
  name: string;
  /**
   * 步骤进度
   */
  progress: number;
};
