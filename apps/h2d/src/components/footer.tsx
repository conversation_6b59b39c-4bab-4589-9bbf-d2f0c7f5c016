import { Link } from 'tdesign-react';
import { ReactNode } from 'react';

import './footer.less';

const Footer = (props: {
  list: {
    text?: ReactNode;
    link?: string;
    isLine?: boolean;
    isLight?: boolean;
    isHover?: boolean;
    onClick?: Function;
  }[];
  style?: React.CSSProperties;
}) => {
  return (
    <>
      <div className={`genie-footer`} style={props.style}>
        <div className={`genie-footer__list`}>
          {!!props.list &&
            props.list.map((item, index) => {
              return (
                <div
                  className={`genie-footer__item ${item.isLight ? 'is-light' : ''}  ${item.isHover ? 'is-hover' : ''}`}
                  key={index}
                >
                  {!!item.link && (
                    <Link hover="color" underline={false} href={item.link} target="_blank">
                      {item.text}
                    </Link>
                  )}
                  {!item.link && (
                    <div
                      className={`genie-footer__item-text ${item.onClick ? 'is-click' : ''}`}
                      onClick={() => {
                        if (item.onClick) {
                          item.onClick();
                        }
                      }}
                    >
                      {item.text}
                    </div>
                  )}

                  {!!item.isLine && <div className={`genie-footer__item-line`}></div>}
                </div>
              );
            })}
        </div>
      </div>
    </>
  );
};

export default Footer;
