.genie-footer {
  position: absolute;
  bottom: 11px;
  left: 50%;
  transform: translateX(-50%);

  &__list {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 8px;
  }

  &__item {
    display: flex;
    align-items: center;

    &-text,
    .t-link {
      color: rgba(255, 255, 255, 0.50);
      font-family: "PingFang SC";
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      white-space: nowrap;

    }

    &-text.is-click {
      &:hover {
        color: #fff;
      }
    }


    .t-link {
      &:hover {
        color: rgba(#FFF, 0.80);
      }

      &.t-link--hover-underline:hover::after {
        border-color: rgba(255, 255, 255, 0.50);
      }
    }

    &-line {
      width: 1px;
      height: 11px;
      background: rgba(255, 255, 255, 0.50);
      margin-left: 10px;
      margin-right: 2px;
    }

    &.is-light {
      .genie-footer__item-text,
      .t-link {
        color: #FFF;
      }
    }

    &.is-hover {

      .genie-footer__item-text,
      .t-link {
        cursor: pointer;

        &:hover {
          color: rgba(255, 255, 255, 0.80);
        }
      }
    }
  }
}
