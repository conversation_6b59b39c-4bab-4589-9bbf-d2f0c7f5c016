import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { locales } from '@ui/locale';

import Style from './language.module.less';

import imgLocale from '@ui/assets/locale.svg';

/**
 * 语言切换组件
 * @returns
 */
const Locale = () => {
  const { i18n } = useTranslation();

  const handleSwitch = () => {
    i18n.changeLanguage(i18n.language === locales[0] ? locales[1] : locales[0]);
  };

  return (
    <div className={Style.locale}>
      <img onClick={handleSwitch} src={imgLocale} className={Style.localeImg} />
    </div>
  );
};

export default memo(Locale);
