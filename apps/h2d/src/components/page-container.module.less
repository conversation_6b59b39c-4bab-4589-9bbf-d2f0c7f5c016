@import '../styles/vars.less';

.pageContainer {
  display: flex;
  flex-direction: column;

  box-sizing: border-box;
  width: 100%;
  height: 97vh;
  padding: 16px 20px;

  .pageTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;

    width: 100%;

    color: #ffffff;

    font-size: 30px;
    font-weight: 700;
    line-height: 30px;
  }

  .pageDesc {
    width: 100%;
    margin-top: 16px;

    color: @text-color-2;

    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }

  .pageTab {
    margin-top: 16px;
    margin-bottom: 8px;
    margin-left: -20px;
    margin-right: -20px;
    padding: 8px 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    background: #292A2E;

    .url {
      padding: 0 10px;
    }

    .browser {
      padding: 0 10px;
    }

    .html {
      padding: 0 10px;
    }

    .text {
      padding: 0 10px;
    }

    .pageTabItem {
      height: 24px;
      font-size: 11px;
      line-height: 22px;
      text-align: center;
      border-radius: 16px;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.40);
      border: 1px solid rgba(255, 255, 255, 0.40);
      box-sizing: border-box;
    }

    .pageTabItemActive {
      background: #61f29f;
      color: #000000;
      border: 1px solid #61f29f;
    }
  }

  .pageContent {
    flex: 1;

    box-sizing: border-box;
    width: 100%;
    height: 0;
  }

  .pageFooter {
    text-align: center;

    color: rgba(255, 255, 255, 0.4);

    font-size: 12px;
    line-height: 22px;
  }
}
