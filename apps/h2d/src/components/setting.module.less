@import '../styles/vars.less';

.settingPopup {
  border: none;
  background-color: #202226;

  :global(.t-dropdown__item--theme-default) {
    padding: 2px 8px;

    color: @text-color-1 !important;

    font-size: 12px;
    line-height: 24px;
    --ripple-color: #202226;
  }

  :global(.t-dropdown__submenu) {
    border: none;
    background-color: #202226;

    font-size: 12px;
  }

  :global(.t-dropdown__item--theme-default:hover) {
    background-color: rgba(229, 231, 244, 0.1);
  }
}

.inputTipItem {
  &:hover {
    color: #ffffff;
  }
}
