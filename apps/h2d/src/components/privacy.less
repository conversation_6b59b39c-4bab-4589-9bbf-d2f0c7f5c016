.privacy {
  width: 100%;
  height: 100%;
  color: #FFFFFF;
  z-index: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;

  .header {
    font-weight: 500;
  }

  .nav {
    display: flex;
    justify-content: left;
    align-items: center;
    height: 40px;
    padding-left: 4px;
    cursor: pointer;

    .back span {
      position: relative;
      padding-left: 4px;
      top: 2px;
    }
  }

  .top-header {
    flex: 1;
    padding: 0 32px;
    margin-top: 10px;
  }

  .privacy-container {
    width: 100%;
    padding: 0 32px;
    box-sizing: border-box;
    overflow-y: auto;
    margin: 10px 0 40px;
    // background-color: #25262f;
    scrollbar-width: none;

    font-family: "PingFang SC";
    font-size: 12px;
    line-height: 20px;

    .mid-content {
      height: 40vh;
    }

    .content {
      text-indent: 2em;
    }

    .top {
      margin-bottom: 10px;
    }
  }
}
