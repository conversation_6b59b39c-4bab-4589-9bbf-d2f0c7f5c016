@import '../styles/vars.less';

.scrollList {
  position: relative;

  overflow: hidden;

  width: 100%;
  height: 100%;

  white-space: nowrap;
}

.scrollFlexList {
  position: relative;

  overflow: hidden;

  width: 100%;
  height: auto;
  max-height: 100%;

  .scrollContent {
    display: inline-flex;

    gap: 10px;
  }
}

.scrollContent {
  display: inline-block;

  box-sizing: border-box;
  padding: 16px 20px;
}

.scrollVertical {
  .scrollContent {
    flex-direction: column;

    width: 100%;
  }
}

.scrollToBottom {
  position: absolute;
  z-index: 3;
  right: 10px;
  bottom: 10px;

  display: flex;
  align-items: center;
  justify-content: center;

  width: 32px;
  height: 32px;

  cursor: pointer;

  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
}
