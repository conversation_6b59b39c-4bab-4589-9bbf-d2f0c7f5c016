import React, { memo, PropsWithChildren, useMemo, useState } from 'react';
import { Dropdown, DropdownOption } from 'tdesign-react';

import Style from './setting.module.less';
import { useUserStore } from '@ui/store';
import { messages } from '@worker/index';
import { useNavigate } from 'react-router-dom';

/**
 * 设置组件属性
 */
export type SettingProps = PropsWithChildren<{}>;

const options = [
  // {
  //   content: '切换语言',
  //   value: 1,
  //   children: [
  //     {
  //       content: '简体中文',
  //       value: 'chinese',
  //     },
  //     {
  //       content: 'English',
  //       value: 'english',
  //     },
  //   ],
  // },
  {
    content: '隐私政策',
    value: 'policy',
  },
  // {
  //   content: '帮助文档',
  //   value: 'help',
  // },
  {
    content: '退出登录',
    value: 'logout',
  },
];
/**
 *  设置组件
 * @returns
 */
const Setting = (props: SettingProps) => {
  const navigate = useNavigate();

  const { setShowPolicy, setShowHelp } = useUserStore();

  const handleClick = (item: DropdownOption) => {
    if (item.value === 'policy') {
      setShowPolicy(true);
    } else if (item.value === 'help') {
      setShowHelp(true);
    } else if (item.value === 'logout') {
      messages['set-storage'].send({ key: 'user-token', value: '' });
      navigate('/login');
    }
  };
  return (
    <Dropdown
      options={options}
      direction="left"
      trigger="click"
      onClick={handleClick}
      popupProps={{
        placement: 'top-left',
        overlayInnerClassName: Style.settingPopup,
      }}
    >
      <span className={Style.inputTipItem} style={{ cursor: 'pointer' }}>
        设置
      </span>
    </Dropdown>
  );
};

export default memo(Setting);
