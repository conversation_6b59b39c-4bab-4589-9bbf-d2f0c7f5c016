import React, { memo } from 'react';
import { ChevronLeftIcon } from 'tdesign-icons-react';
import { useUserStore } from '@ui/store';
import ScrollList from './scroll-list';

import { html } from '../assets/policy.md';
import Style from './policy.module.less';

/**
 * 协议组件
 * @returns
 */
const Protocal = () => {
  const { showPolicy, setShowPolicy } = useUserStore();

  const handleBack = () => {
    setShowPolicy(false);
  };

  return (
    <>
      {showPolicy && (
        <div className={Style.policy}>
          <div className={Style.policyHeader}>
            <div className={Style.policyBack} onClick={handleBack}>
              <ChevronLeftIcon size='medium' style={{ marginRight: '5px' }} />
              <span>返回</span>
            </div>
          </div>
          <div className={Style.policyContent}>
            <ScrollList>
              <div className={Style.policyTitle}>AI设计小助手隐私协议</div>
              <div className={Style.policyMarkdown} dangerouslySetInnerHTML={{ __html: html }} />
            </ScrollList>
          </div>
        </div>
      )}
    </>
  );
};

export default memo(Protocal);
