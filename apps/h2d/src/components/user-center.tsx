import { memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { CloseIcon, ChevronDownIcon } from 'tdesign-icons-react';
import { useTranslation } from 'react-i18next';

import { MessageTypes } from '@ai-assitant/ai-core';
import { useUserStore } from '@ui/store';
import { messages } from '@worker/index';
import { OFFICAL_URL, SUPPORT_URL } from '@ui/utils/constants';
import Avatar from './avatar';

import Style from './user-center.module.less';

const UserCenter = () => {
  const navigate = useNavigate();

  const { t } = useTranslation();

  const { userInfo, showCenter, setShowCenter } = useUserStore();

  const handleClose = () => {
    setShowCenter(false);
  };

  const handleLogout = () => {
    setShowCenter(false);
    messages[MessageTypes.SET_STORAGE].send({ key: 'user-token', value: '' });
    navigate('/login');
  };

  const handleVisit = () => {
    window.open(OFFICAL_URL);
  };

  const handleContact = () => {
    window.open(SUPPORT_URL);
  };

  return (
    <>
      {showCenter && (
        <>
          <div className={Style.userCenter}>
            <div className={Style.userCenterHeader}>
              <span></span>
              <span className={Style.userCenterClose} onMouseUp={handleClose}>
                <CloseIcon size={'medium'} />
              </span>
            </div>
            <div className={Style.userCenterContent}>
              <Avatar size={40} fontSize={24} />
              <div className={Style.userName}>
                <span>{userInfo.display_name || ''}</span>
              </div>
              <div className={Style.userCenterBtn}>
                <span onClick={handleVisit}>{t('settings.visit')}</span>
                <ChevronDownIcon size={'medium'} />
              </div>
              <div onClick={handleLogout} className={Style.userCenterBtn}>
                <span>{t('settings.logout')}</span>
              </div>
              <span onClick={handleContact} className={Style.userCenterLink}>
                {t('settings.contact')}
              </span>
            </div>
          </div>
          <div className={Style.userMask}></div>
        </>
      )}
    </>
  );
};

export default memo(UserCenter);
