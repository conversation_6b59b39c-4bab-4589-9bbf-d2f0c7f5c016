import { ReactNode } from 'react';

import './submitCard.less';

const SubmitCard = (props: {
  title?: string;
  input?: ReactNode;
  confirmBtn?: ReactNode;
  cancelBtn?: ReactNode;
  style?: React.CSSProperties;
}) => {
  return (
    <>
      <div className={`genie-submit-card`} style={props.style}>
        <div className={`genie-submit-card__title`}>{props.title}</div>
        <div className={`genie-submit-card__input`}>{props.input}</div>
        <div className={`genie-submit-card__btns`}>
          <div className={`genie-submit-card__btns-confirm`}>{props.confirmBtn}</div>
          <div className={`genie-submit-card__btns-cancel`}>{props.cancelBtn}</div>
        </div>
      </div>
    </>
  );
};

export default SubmitCard;
