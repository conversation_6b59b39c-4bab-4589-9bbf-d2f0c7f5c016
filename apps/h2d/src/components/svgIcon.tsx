import { memo, PropsWithChildren } from 'react';
import clsx from 'clsx';
import Style from './svg-icon.module.less';

/**
 * Svg图标组件属性
 */
export type SvgIconProps = PropsWithChildren<{
  /**
   * 图标名称
   */
  name: string;
  /**
   * 图标前缀
   */
  prefix?: string;
  /**
   * 图标样式
   */
  iconStyle?: React.CSSProperties;
  /**
   * 图标是否高亮
   */
  active?: boolean;
  /**
   * 鼠标悬停是否修改颜色
   */
  hover?: boolean;
  className?: string;
  /**
   * 图标颜色
   */
  color?: string;
}>;

/**
 * Svg图标组件
 * @param props
 * @returns
 */
const SvgIcon = (props: SvgIconProps) => {
  const { name, prefix = 'icon', iconStyle = {}, hover = true, active = false, className, color } = props;

  const symbolId = `#${prefix}-${name}`;
  return (
    <svg
      className={clsx(Style.svgIcon, { [Style.svgIconHover]: hover }, className)}
      aria-hidden="true"
      style={{ color: active ? '#61f29f' : (color ?? '#ffffff'), ...iconStyle }}
    >
      <use href={symbolId} />
    </svg>
  );
};

export default memo(SvgIcon);
