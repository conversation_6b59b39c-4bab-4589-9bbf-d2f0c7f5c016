import React, { memo, PropsWithChildren, useState } from 'react';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import { useNavigate } from 'react-router-dom';

import Language from './language';

import Style from './page-container.module.less';
import { VERSION } from '@ui/utils/config';
import Footer from '@ui/components/footer';
import PrivacyAgreement from '@ui/components/privacyAgreement';

/**
 * 页面容器组件属性
 */
export type PageContainerProps = PropsWithChildren<{
  /**
   * 页面
   */
  page: string;
  /**
   * 当前激活的tab
   */
  activeTab?: string;
  /**
   * 是否显示标题
   */
  showTitle?: boolean;
  /**
   * 是否显示页面描述
   */
  showDesc?: boolean;
  /**
   * 底部信息
   */
  footer?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}>;

/**
 *  页面容器组件
 * @returns
 */
const PageContainer = ({
  page,
  activeTab,
  children,
  showTitle = true,
  showDesc = true,
  footer,
  className,
  style,
}: PageContainerProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showPrivacy, setShowPrivacy] = useState(false);
  const footerData = [
    {
      text: `${t('footer.version')}${VERSION}`,
      isLine: true,
    },
    {
      text: `${t('footer.contact')}`,
      link: 'https://support.qq.com/products/134728',
    },
    {
      text: `${t('footer.agreement')}`,
      isHover: true,
      onClick: () => {
        setShowPrivacy(true);
      },
    },
  ];
  return (
    <div className={clsx(Style.pageContainer, className)} style={style}>
      {showTitle && (
        <div className={Style.pageTitle}>
          <span>{t(`${page}.title`)}</span>
          <Language />
        </div>
      )}
      {showDesc && <div className={Style.pageDesc}>{t(`${page}.desc`)}</div>}

      {showTitle && (
        <div className={Style.pageTab}>
          <div
            className={clsx(Style.pageTabItem, activeTab === 'url' && Style.pageTabItemActive, Style.url)}
            onClick={() => navigate('/url')}
          >
            {t('page.fromUrl')}
          </div>
          <div
           className={clsx(Style.pageTabItem, activeTab === 'plugin' && Style.pageTabItemActive, Style.browser)}
           onClick={() => navigate('/plugin')}
          >
           {t('page.fromBrowser')}
          </div>
          <div
            className={clsx(Style.pageTabItem, activeTab === 'html' && Style.pageTabItemActive, Style.html)}
            onClick={() => navigate('/html')}
          >
            {t('page.fromHtml')}
          </div>
          {/*<div*/}
          {/*  className={clsx(Style.pageTabItem, activeTab === 'text' && Style.pageTabItemActive, Style.text)}*/}
          {/*  onClick={() => navigate('/text')}*/}
          {/*>*/}
          {/*  {t('page.fromText')}*/}
          {/*</div>*/}
        </div>
      )}
      <div className={Style.pageContent}>{children}</div>
      <Footer style={{ marginTop: '80px' }} list={footerData} />
      {!!showPrivacy && <PrivacyAgreement onClosePrivacy={() => setShowPrivacy(false)} />}
    </div>
  );
};

export default memo(PageContainer);
