import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessagePlugin } from 'tdesign-react';

import { useUserStore } from '@ui/store';
import { getCustomHeader } from '@ui/utils/request';
import track from '@ui/utils/track';
import { messages } from '@worker/index';
import { FigmaResult, MessageTypes, PlatformUser } from '@ai-assitant/ai-core';

export default function useCurrentUser() {
  const { userToken, userInfo, setUserInfo } = useUserStore();

  /**
   * 监听 Token 变化，尝试获取用户信息
   */
  useEffect(() => {
    new Promise(async () => {
      const result = (await messages[MessageTypes.GET_PLATFORM_USER].request({})) as FigmaResult<PlatformUser>;
      if (result.success && result.data) {
        setUserInfo(result.data);
        track.initTrackUser(result.data.userId, result.data.userName, '');
      }
    });
  }, [userToken]);

  return {
    userInfo,
  };
}
