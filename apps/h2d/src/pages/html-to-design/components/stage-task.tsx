import { memo, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import { StageTask as StageTaskType } from '@ui/components/stage/stageManager';
import SvgIcon from '@ui/components/svgIcon';

import Style from './stage-task.module.less';

/**
 * 任务信息组件属性
 */
export type StageTaskProps = PropsWithChildren<{
  task?: StageTaskType;
}>;

/**
 * 任务信息组件
 * @returns
 */
const StageTask = (props: StageTaskProps) => {
  const { t } = useTranslation();

  const { task } = props;

  // 宽度
  const width = task?.name.split(':')[0].split('x')[0] || '';
  // 主题
  const theme = task?.name.split(':')[0].split('x')[1] || '';

  return (
    <div className={Style.stageTask}>
      {task?.state === 'Success' && <SvgIcon name="stage-finish" iconStyle={{ width: '16px', height: '16px' }} />}
      {task?.state === 'Failed' && <SvgIcon name="stage-failed" iconStyle={{ width: '16px', height: '16px' }} />}
      <div className={Style.stageTaskInfo}>
        <span className={task?.state === 'Success' ? Style.taskSuccess : Style.taskFailed}>
          {t('inspire.html.stage.frameWidth')}:{width}；{t('inspire.html.stage.theme')}:{theme}；
          {task?.state === 'Success'
            ? t('inspire.html.stage.generateSuccess')
            : t('inspire.html.stage.generateFailed') + ' ' + (task?.errorMessage || '')}
        </span>
      </div>
    </div>
  );
};

export default memo(StageTask);
