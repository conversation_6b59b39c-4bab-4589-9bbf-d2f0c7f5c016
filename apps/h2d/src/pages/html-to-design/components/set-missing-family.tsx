import { memo, useEffect, useRef, useState } from 'react';
import { Select, Drawer, Button } from 'tdesign-react';
import { useTranslation } from 'react-i18next';

import htmlParser from '@ui/utils/html-parser';
import { FamilyMap, MissingFamily } from '@tencent/h2d-html-parser';
import { useFamilyStore } from '@ui/store';
import { MessageTypes } from '@ai-assitant/ai-core';
import { messages } from '@worker/index';
import ScrollList from '@ui/components/scroll-list';
import SvgIcon from '@ui/components/svgIcon';

import Style from './family.module.less';

/**
 * 缺失字体项
 */
type MissingFontItem = {
  // 字体名称
  key: string;
  // 字体显示
  label: string;
  // 下拉选择框
  select: JSX.Element;
};

const SetMissingFamily = () => {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);
  const missingFamily = useRef<MissingFamily>();
  const [missingFontOptions, setMissingFontOptions] = useState<MissingFontItem[]>([]);

  const { familyMap, setFamilyMap } = useFamilyStore();


  useEffect(() => {
    if (familyMap === null) {
      initFamilyMap();
    }

    htmlParser.fontMissingEmitter.on((data) => {
      // 没有缺少字体的直接忽略当前setMissingFamily流程
      if (!data.missingFamily || Object.keys(data.missingFamily).length === 0) {
        htmlParser.fontSetEmitter.emit({});
        return;
      }

      const fontOptions = Object.keys(data.availableFamily).map((key) => {
        const fontName = data.availableFamily[key];
        return {
          label: fontName.family,
          value: fontName.family,
        };
      });

      const fontList = Object.keys(data.missingFamily).map((key: string) => {
        const mappingFamily = data.missingFamily[key];
        return {
          label: key,
          key: key,
          select: (
            <Select
              key={key}
              style={{ width: '180px' }}
              size="small"
              prefixIcon={<>Font</>}
              className={Style.fontSettingItemSelect}
              filterable
              showArrow={false}
              defaultValue={mappingFamily}
              popupProps={{ overlayClassName: Style.fontSettingPopup }}
              options={fontOptions}
              onChange={(value) => handleChange(key, value as string)}
            ></Select>
          ),
        } as MissingFontItem;
      });

      setMissingFontOptions(fontList);

      missingFamily.current = data.missingFamily;

      setShow(true);
    });

    return () => {
      htmlParser.fontMissingEmitter.off();
    };
  }, []);

  const initFamilyMap = async () => {
    const result = (await messages[MessageTypes.GET_FONT_LIST].request({})) as FamilyMap;

    setFamilyMap(result);
  };

  const handleChange = (key: string, value: string) => {
    missingFamily.current = { ...missingFamily.current, [key]: value };
  };

  const handleSet = () => {
    setShow(false);
    if (missingFamily.current) {
      htmlParser.fontSetEmitter.emit(missingFamily.current);
    }
  };

  return (
    <>
      {show && (
        <Drawer
          closeBtn={false}
          closeOnEscKeydown
          closeOnOverlayClick
          footer={false}
          header={false}
          mode="overlay"
          className={Style.fontSettingDrawer}
          placement="bottom"
          preventScrollThrough
          showOverlay
          visible={true}
        >
          <div className={Style.fontSettingBox}>
            <div className={Style.fontSettingTitle}>
              <SvgIcon name="font-logo" iconStyle={{ width: '16px', height: '16px' }} />
              <span>{t('inspire.html.font.title')}</span>
            </div>
            <div className={Style.fontSettingContent}>
              <ScrollList flex={true} padding={'0px'} direction={'vertical'}>
                {missingFontOptions.map((item, index) => {
                  return (
                    <div className={Style.fontSettingItem} key={item.key}>
                      <div className={Style.fontSettingItemLabel}>{item.label}</div>
                      <div className={Style.fontSettingItemContent}>
                        <SvgIcon name="font-arrow" iconStyle={{ width: '12px', height: '6px' }} />
                        {item.select}
                      </div>
                    </div>
                  );
                })}
              </ScrollList>
            </div>
            <Button onClick={handleSet}>{t('inspire.html.font.confirm')}</Button>
          </div>
        </Drawer>
      )}
    </>
  );
};
export default memo(SetMissingFamily);
