import { memo, PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';

import Style from './stage-info.module.less';

/**
 * 进度信息组件属性
 */
export type StageProps = PropsWithChildren<{
  /**
   * 类型,url/plugin/html/text
   */
  type: string;
  /**
   * 加载页面地址
   */
  loadingPage: string;
  /**
   * 视图
   */
  viewports: string;
  /**
   * 主题
   */
  themes: string;
}>;

/**
 * 进度信息组件
 * @returns
 */
const StageInfo = (props: StageProps) => {
  const { t } = useTranslation();

  const { loadingPage, viewports, themes, type } = props;

  return (
    <div className={Style.htmlStageInfo}>
      <div className={Style.htmlStageInfoItem}>
        <div style={{ textWrap: 'nowrap' }}>{type === 'url' ? t('inspire.html.stage.loadingPage') + ':' : 'HTML:'}</div>
        <div className={Style.stageHighlight}>{type === 'url' ? loadingPage : loadingPage.slice(0, 25)}...</div>
      </div>
      <div className={Style.htmlStageInfoItem}>
        <div className={Style.htmlStageInfoSubItem}>
          <div style={{ textWrap: 'nowrap' }}>{t('inspire.html.stage.viewports') + ':'}</div>
          <div className={Style.stageHighlight}>{viewports}</div>
        </div>
        {type === 'url' && (
          <div className={Style.htmlStageInfoSubItem}>
            <div style={{ textWrap: 'nowrap' }}>{t('inspire.html.stage.themes') + ':'}</div>
            <div className={Style.stageHighlight}>{themes}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(StageInfo);
