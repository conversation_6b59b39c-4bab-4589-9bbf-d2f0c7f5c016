@import '@src/styles/vars.less';

.fontSettingDrawer {
  :global(.t-drawer__content-wrapper) {
    max-height: 60vh;
    background-color: @bg-color;
  }

  .fontSettingBox {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 16px;
    border: solid 1px @border-color-1;
    border-radius: 8px;
    background-color: @bg-color-1;
    font-size: 14px;
    font-weight: 500;
    overflow: hidden; /* 防止内容溢出 */
  }

  .fontSettingTitle {
    display: flex;
    align-items: center;

    margin-bottom: 10px;

    color: @text-color-1;

    gap: 10px;
  }

  .fontSettingContent {
    display: flex;
    align-items: center;
    flex: 1;
    flex-direction: column;
    gap: 10px;
    overflow: auto; /* 添加滚动条 */
    margin-bottom: 16px; /* 为底部按钮留出空间 */
    min-height: 0; /* 确保flex子项可以正确滚动 */
  }

  .fontSettingItem {
    display: flex;
    align-items: center;
    justify-content: space-between;

    width: 100%;
    margin-bottom: 10px;

    &Label {
      color: #ffffff;

      font-size: 14px;
      line-height: 22px;
    }

    &Content {
      display: flex;
      align-items: center;

      gap: 10px;
    }

    &Select {
      :global(.t-input) {
        color: #ffffff;
        border: solid 1px rgba(255, 255, 255, 0.1);
        background-color: transparent;
        box-shadow: none;

        font-size: 14px;
      }

      :global(.t-input:focus) {
        border: solid 1px rgba(255, 255, 255, 0.1);
      }

      :global(.t-input__inner) {
        color: #ffffff;
      }

      :global(.t-input__inner::placeholder) {
        opacity: 0.5;
        color: #ffffff;
      }

      :global(.t-input__prefix) {
        color: rgba(255, 255, 255, 0.4);

        font-size: 14px;
      }

      .t-input__inner::placeholder {
        opacity: 0.5;
      }
    }
  }
}

.fontSettingPopup {
  :global(.t-popup__content) {
    background-color: @bg-color;
  }

  :global(.t-select-option) {
    color: #dcdcdc;
  }

  :global(.t-select-option:not(.t-is-selected):hover) {
    background-color: @bg-color-1;
  }
}
