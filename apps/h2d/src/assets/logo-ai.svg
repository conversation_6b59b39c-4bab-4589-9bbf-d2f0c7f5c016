<svg xmlns="http://www.w3.org/2000/svg" width="436" height="436" fill="none" viewBox="0 0 436 436">
  <circle cx="218" cy="218" r="218" fill="url(#a)" opacity=".6"/>
  <circle cx="218" cy="218" r="183" fill="url(#b)" opacity=".8"/>
  <circle cx="218" cy="218" r="149" fill="url(#c)" opacity=".8"/>
  <path fill="#D6FFE9" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M292.915 194.496a3 3 0 0 1-3.695 3.743c-7.312-2.079-17.547-5.509-24.164-9.028-1.666-.887-3.198-1.828-4.425-2.812-1.152-.923-2.389-2.158-2.942-3.726a4.871 4.871 0 0 1-.147-2.854c.259-1.006.817-1.819 1.472-2.449 1.226-1.181 3.02-1.946 5.005-2.507 8.587-2.427 15.381.564 20.157 5.103 4.656 4.427 7.501 10.378 8.739 14.53Z"/>
  <path fill="#D6FFE9" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M379.3 251.298c.008.051.015.103.02.155.599 5.692-.605 14.238-6.074 21.797-5.549 7.668-15.254 14-31.031 15.511-18.868 1.806-35.204 2.428-48.702 1.048-13.471-1.378-24.462-4.782-32.322-11.332-8.013-6.677-11.968-16.984-12.182-27.5-.214-10.527 3.305-21.574 10.64-29.988 14.027-16.089 31.945-26.979 55.313-28.231.061-.003.123-.004.184-.004 9.262.071 23.405 3.097 36.196 11.902 12.886 8.87 24.303 23.549 27.958 46.642Z"/>
  <path fill="#87E4B2" d="M367.502 252.888c-6.067-38.389-37.686-48.217-52.737-48.332-19.302 1.036-34.124 10.012-45.843 23.476-11.719 13.464-11.374 35.558 1.034 45.915 12.409 10.357 35.503 11.393 67.903 8.286 25.92-2.486 30.562-20.599 29.643-29.345Z"/>
  <mask id="d" width="108" height="80" x="260" y="204" maskUnits="userSpaceOnUse" style="mask-type:alpha">
    <path fill="#87E4B2" d="M367.552 252.888c-6.07-38.389-37.703-48.217-52.762-48.332-19.311 1.036-34.139 10.012-45.864 23.476-11.725 13.464-11.38 35.558 1.035 45.915 12.414 10.357 35.519 11.393 67.934 8.286 25.932-2.486 30.576-20.599 29.657-29.345Z"/>
  </mask>
  <g mask="url(#d)">
    <ellipse cx="66.84" cy="22.28" fill="#438B64" rx="66.84" ry="22.28" transform="matrix(-1 0 0 1 380.861 261.494)"/>
    <circle cx="11.553" cy="11.553" r="11.553" fill="#C3EBFD" transform="matrix(-1 0 0 1 295.042 207.857)"/>
  </g>
  <path fill="#9D2C2B" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M347.869 236.495c8.321-9.602 16.188-5.883 18.804 2.401 5.201 27.206-10.803 38.808-33.608 40.009-22.805 1.2-52.812-17.604-45.61-36.008 5.761-14.724 18.671-8.802 24.405-4.001 3.468 3.467 13.123 10.482 24.006 10.802 10.882.32 9.922-10.802 12.003-13.203Z"/>
  <mask id="e" width="73" height="45" x="291" y="228" maskUnits="userSpaceOnUse" style="mask-type:alpha">
    <path fill="#9D2C2B" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M339.669 237.825a3.003 3.003 0 0 1 .77-2.404c1.817-1.974 3.771-3.269 5.836-3.872 2.086-.61 4.071-.449 5.81.144 3.338 1.14 5.753 3.845 6.994 5.848.187.302.319.635.389.983 2.065 10.171-.003 18.048-5.492 23.425-5.327 5.217-13.35 7.545-22.11 7.979-9.139.453-19.523-2.832-26.982-7.923-3.741-2.553-6.946-5.696-8.839-9.275-1.934-3.658-2.5-7.814-.769-11.979 1.246-2.997 2.988-5.196 5.163-6.594 2.177-1.399 4.545-1.839 6.769-1.731 4.303.209 8.332 2.462 10.713 4.339.069.055.135.112.199.172 2.266 2.133 8.725 6.5 15.748 6.694 3.109.087 4.376-1.016 4.997-1.965.765-1.17.941-2.749.804-3.841Z"/>
  </mask>
  <g mask="url(#e)">
    <ellipse cx="49.344" cy="15.487" fill="#FB6464" rx="49.344" ry="15.487" transform="matrix(-1 0 0 1 372.614 251.406)"/>
  </g>
  <ellipse cx="6.401" cy="2.401" fill="#FFA9A9" rx="6.401" ry="2.401" transform="matrix(-1 0 0 1 326.849 229.812)"/>
  <ellipse cx="6.401" cy="2.401" fill="#FFA9A9" rx="6.401" ry="2.401" transform="matrix(-1 0 0 1 326.849 229.812)"/>
  <ellipse cx="6.401" cy="2.401" fill="#FFA9A9" rx="6.401" ry="2.401" transform="matrix(-1 0 0 1 326.849 229.812)"/>
  <ellipse cx="5.201" cy="2.401" fill="#FFA9A9" rx="5.201" ry="2.401" transform="matrix(-1 0 0 1 344.453 229.012)"/>
  <ellipse cx="5.201" cy="2.401" fill="#FFA9A9" rx="5.201" ry="2.401" transform="matrix(-1 0 0 1 344.453 229.012)"/>
  <ellipse cx="5.201" cy="2.401" fill="#FFA9A9" rx="5.201" ry="2.401" transform="matrix(-1 0 0 1 344.453 229.012)"/>
  <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="8" d="M331.65 207.757c3.558 3.257 10.016 11.978 7.389 20.804m-24.193-20.804c3.558 3.257 10.017 11.978 7.389 20.804"/>
  <path stroke="#000" stroke-linejoin="round" stroke-width="6" d="M347.869 236.495c8.321-9.602 16.188-5.883 18.804 2.401 5.201 27.206-10.803 38.808-33.608 40.009-22.805 1.2-52.812-17.604-45.61-36.008 5.761-14.724 18.671-8.802 24.405-4.001 3.468 3.467 13.123 10.482 24.006 10.802 10.882.32 9.922-10.802 12.003-13.203Z"/>
  <path fill="#D6FFE9" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M139.376 197.504a3 3 0 0 0 3.696 3.743c7.315-2.081 17.553-5.512 24.173-9.033 1.667-.886 3.2-1.828 4.427-2.812 1.151-.923 2.389-2.159 2.943-3.727a4.875 4.875 0 0 0 .146-2.855c-.258-1.006-.817-1.818-1.472-2.449-1.226-1.181-3.021-1.946-5.006-2.508-8.591-2.427-15.386.564-20.164 5.106-4.658 4.427-7.504 10.381-8.743 14.535Z"/>
  <path fill="#D6FFE9" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M56.16 254.327a3.161 3.161 0 0 0-.02.155c-.6 5.694.606 14.244 6.077 21.805 5.55 7.671 15.259 14.005 31.042 15.516 18.876 1.808 35.217 2.43 48.721 1.049 13.476-1.378 24.471-4.784 32.334-11.336 8.015-6.679 11.972-16.99 12.186-27.51.214-10.531-3.306-21.581-10.644-29.999-14.032-16.095-31.957-26.989-55.334-28.241a2.793 2.793 0 0 0-.183-.004c-9.266.07-23.415 3.098-36.21 11.906-12.891 8.873-24.312 23.558-27.968 46.659Z"/>
  <path fill="#87E4B2" d="M67.962 255.918c6.069-38.404 37.7-48.236 52.757-48.351 19.31 1.036 34.137 10.016 45.861 23.485 11.724 13.469 11.379 35.572-1.035 45.933-12.413 10.361-35.516 11.397-67.929 8.289-25.93-2.487-30.574-20.607-29.654-29.356Z"/>
  <mask id="f" width="109" height="80" x="67" y="207" maskUnits="userSpaceOnUse" style="mask-type:alpha">
    <path fill="#87E4B2" d="M67.913 255.918c6.072-38.404 37.718-48.236 52.782-48.351 19.319 1.036 34.153 10.016 45.882 23.485s11.384 35.573-1.035 45.934c-12.419 10.36-35.533 11.397-67.96 8.288-25.943-2.486-30.589-20.606-29.669-29.356Z"/>
  </mask>
  <g mask="url(#f)">
    <ellipse cx="121.464" cy="286.816" fill="#438B64" rx="66.866" ry="22.289"/>
    <circle cx="152.008" cy="222.426" r="11.557" fill="#C3EBFD"/>
  </g>
  <path fill="#fff" stroke="#000" stroke-width="6" d="M74.802 240.171c9.606 4.535 21.072.424 25.607-9.183 4.535-9.606.424-21.072-9.183-25.607-9.606-4.535-21.072-.423-25.606 9.183-4.536 9.606-.424 21.072 9.182 25.607Z"/>
  <path fill="#262E36" d="M85.786 219.704a6.684 6.684 0 1 1-12.09-5.708 6.684 6.684 0 1 1 12.09 5.708Z"/>
  <path fill="#fff" stroke="#000" stroke-width="6" d="M110.826 240.171c9.606 4.535 21.071.424 25.607-9.183 4.535-9.606.423-21.072-9.183-25.607s-21.072-.423-25.607 9.183-.424 21.072 9.183 25.607Z"/>
  <path fill="#262E36" d="M121.809 219.704a6.684 6.684 0 0 1-8.898 3.191 6.683 6.683 0 0 1-3.191-8.899 6.683 6.683 0 1 1 12.089 5.708Z"/>
  <path fill="#D6FFE9" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M239.125 216.08a3 3 0 0 0 3.696 3.743c9.127-2.596 21.854-6.865 30.05-11.224 2.062-1.097 3.932-2.248 5.415-3.437 1.407-1.128 2.818-2.564 3.438-4.322.335-.948.449-2.022.164-3.131-.281-1.095-.893-1.994-1.633-2.706-1.398-1.346-3.495-2.262-5.939-2.953-10.426-2.946-18.651.669-24.47 6.2-5.7 5.418-9.199 12.728-10.721 17.83Z"/>
  <path fill="#D6FFE9" stroke="#000" stroke-linejoin="round" stroke-width="6" d="M131.191 286.969a2.703 2.703 0 0 0-.02.154c-.733 6.958.741 17.448 7.453 26.723 6.791 9.385 18.711 17.208 38.244 19.078 23.564 2.256 43.921 3.028 60.717 1.31 16.767-1.714 30.331-5.943 39.99-13.992 9.81-8.175 14.69-20.83 14.954-33.806.263-12.988-4.079-26.607-13.113-36.97-17.424-19.985-39.638-33.473-68.601-35.024a2.809 2.809 0 0 0-.184-.004c-11.443.087-28.969 3.833-44.815 14.741-15.943 10.974-30.091 29.142-34.625 57.79Z"/>
  <path fill="#87E4B2" d="M145.195 288.839c7.582-47.976 47.097-60.258 65.906-60.401 24.123 1.294 42.645 12.511 57.291 29.337 14.646 16.827 14.215 44.439-1.292 57.382-15.508 12.943-44.368 14.237-84.859 10.354-32.393-3.106-38.194-25.742-37.046-36.672Z"/>
  <mask id="g" width="135" height="100" x="145" y="228" maskUnits="userSpaceOnUse" style="mask-type:alpha">
    <path fill="#87E4B2" d="M145.133 288.839c7.585-47.976 47.118-60.258 65.937-60.401 24.133 1.294 42.664 12.511 57.317 29.337 14.653 16.827 14.222 44.439-1.293 57.382-15.514 12.943-44.389 14.237-84.899 10.354-32.408-3.106-38.211-25.742-37.062-36.672Z"/>
  </mask>
  <g mask="url(#g)">
    <ellipse cx="212.031" cy="327.438" fill="#438B64" rx="83.531" ry="27.844"/>
    <circle cx="250.188" cy="247" r="14.438" fill="#C3EBFD"/>
  </g>
  <path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="10" d="M194 232.438c-4.446 4.07-12.518 14.969-9.235 26m37.235-26c-4.446 4.07-12.518 14.969-9.235 26"/>
  <defs>
    <linearGradient id="a" x1="219.005" x2="219.005" y1="436" y2="0" gradientUnits="userSpaceOnUse">
      <stop stop-color="#87E4B2" stop-opacity="0"/>
      <stop offset="1" stop-color="#87E4B2" stop-opacity=".15"/>
    </linearGradient>
    <linearGradient id="b" x1="218.843" x2="218.843" y1="401" y2="35" gradientUnits="userSpaceOnUse">
      <stop stop-color="#87E4B2" stop-opacity="0"/>
      <stop offset="1" stop-color="#87E4B2" stop-opacity=".15"/>
    </linearGradient>
    <linearGradient id="c" x1="218.687" x2="218.687" y1="367" y2="69" gradientUnits="userSpaceOnUse">
      <stop stop-color="#87E4B2" stop-opacity="0"/>
      <stop offset="1" stop-color="#87E4B2" stop-opacity=".15"/>
    </linearGradient>
  </defs>
</svg>
