<svg xmlns="http://www.w3.org/2000/svg" width="480" height="569" fill="none" viewBox="0 0 480 569">
  <g filter="url(#a)">
    <path fill="#fff" fill-opacity=".08" d="M0 39c92.515-83.787 279.617-12.558 399 29 59.907 20.854 108 17 108 17l-27 484H0S-153.413 177.94 0 39Z" style="mix-blend-mode:overlay"/>
    <path stroke="url(#b)" d="M504.839 85.624a91.747 91.747 0 0 0 1.63-.086L479.527 568.5H.342a449.588 449.588 0 0 1-1.56-4.101 790.303 790.303 0 0 1-4.603-12.641c-3.893-10.959-9.285-26.771-15.275-46.18-11.981-38.819-26.357-92.02-35.94-149.558-9.585-57.541-14.372-119.396-7.19-175.529 7.184-56.135 26.33-106.495 64.562-141.12C46.41-2.36 116.106-5.536 189.34 6.988c66.409 11.357 135.584 35.597 192.429 55.517 5.824 2.04 11.518 4.036 17.067 5.967 30.001 10.444 57.043 14.7 76.587 16.347 9.773.823 17.672.993 23.13.957a164.36 164.36 0 0 0 6.286-.151Z" style="mix-blend-mode:overlay"/>
  </g>
  <defs>
    <linearGradient id="b" x1="400.075" x2="394.379" y1=".037" y2="540.538" gradientUnits="userSpaceOnUse">
      <stop stop-color="#fff" stop-opacity=".6"/>
      <stop offset="1" stop-opacity="0"/>
    </linearGradient>
    <filter id="a" width="695.184" height="688.963" x="-128.184" y="-59.963" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="30"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2163_12460"/>
      <feBlend in="SourceGraphic" in2="effect1_backgroundBlur_2163_12460" result="shape"/>
      <feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset dx="-2" dy="-4"/>
      <feGaussianBlur stdDeviation="21.5"/>
      <feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"/>
      <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
      <feBlend in2="shape" mode="overlay" result="effect2_innerShadow_2163_12460"/>
    </filter>
  </defs>
</svg>
