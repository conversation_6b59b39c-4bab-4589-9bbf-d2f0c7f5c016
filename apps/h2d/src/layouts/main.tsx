import { memo } from 'react';
import { useOutlet } from 'react-router-dom';
import { Layout } from 'tdesign-react';

import Resize from '@ui/components/resize';
import useCurrentUser from '@ui/hooks/useCurrentUser';

import Style from './main.module.less';

export default memo(() => {
  const outlet = useOutlet();


  useCurrentUser();

  const renderOutlet = () => {
    return <Layout.Content>{outlet}</Layout.Content>;
  };

  return (
    <div className={Style.layout}>
      {renderOutlet()}
      <Resize />
    </div>
  );
});
