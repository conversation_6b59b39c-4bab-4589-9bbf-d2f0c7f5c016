{"title": "Design Thinking Tools", "back": "Back", "login": {"brief": "Boost your design workflow with our AI-driven plugin. It's the ultimate efficiency booster for Figma users, offering quick conversions and smart editing tools. Say hello to accelerated productivity and refined designs.", "button": "LOG IN"}, "page": {"fromUrl": "Link", "fromBrowser": "Browser Plugin", "fromHtml": "Code Edit", "fromText": "Text To UI"}, "Dialog": {"Title": "(Optional) Please fill in your improvement suggestions, your feedback will be used to improve the product experience:", "Placeholder": "Please enter", "ConfirmBtn": "Submission of comments", "CancelBtn": "Not yet filled in "}, "menu": {"analysis": "Analysis", "inspire": "Inspire", "generate": "Vivid <PERSON>", "edit": "Edit"}, "analysis": {"title": "Analysis", "desc": "Personalized analysis at your service.", "suggestion": {"title": "\"What content do you want to analyze?\"", "case": "Suggestion:"}, "input": {"placeholder": "Cmd+Enter to newline, type '/' for skills.", "visual": "Brand Message"}, "chat": {"new": "New session", "history": {"title": "History Session"}, "tool": {"copy": "Copy", "refresh": "Refresh", "more": "More", "like": "Like", "unlike": "Unlike"}}, "request": {"loading": "The AI assistant is thinking...", "upload": "Support: JPG, PNG. Limit: Maximum 6 images, 10MB per image Click to upload local files", "optimize": "One-click optimisation of instruction content to help you get Better quality answers"}, "response": {"insert": "Add to Canvas"}, "footer": {"tip": "Content generated by AI, for reference only"}}, "generate": {"title": "Vivid <PERSON>", "desc": "A revolutionary tool for intelligent interface design！", "placeholder": "Describe what you need to generate ...", "hello": "Hi, I'm Unik～", "helloContent": "Get ready for our exciting new page launch!", "beginGenerate": "Start Generate", "failed": "Oops, something went wrong", "stage": {"begin": "Countdown begins", "launchSoon": "The design will launch soon", "generateSuccess": "Generation Success", "generateFailed": "Generation Failure"}}, "inspire": {"title": "Inspire", "desc": "Get ready for our exciting new page launch!", "block": {"beta": "BETA", "hot": "HOT", "url": {"title": "Convert any website into fully editable designs", "desc": "Import websites into Figma to iterate, redesign,benchmark and more; without building each element from scratch.", "btn": "Import"}, "image": {"title": "Convert images into fully editable designs", "desc": "Upload, Transform, Impress: Instantly Transform Screenshots into Stunning Editable Figma Designs with Ease!", "btn": "Import"}}, "html": {"tip": "Import via URL", "help": "Import link to generate a design", "remark": "Import public links (Unable to generate pages that require login)", "input": {"html": "Type or paste HTML code here.", "css": "Type or paste CSS code here. (Optional)"}, "viewports": {"title": "Viewports", "Desktop": "Desktop", "IpadPro": "iPad Pro 12.9", "Iphone": "iPhone 13 mini", "Android": "Android", "Custom": "Custom"}, "themes": {"title": "Themes（Depends on whether the source page supports it）", "light": "light mode", "dark": "dark mode"}, "preferences": {"title": "Generate Preferences", "desc": "Extract page icons / videos", "useAutoLayout": "Use Auto Layout"}, "import": "Import", "font": {"title": "Font Configuration", "confirm": "Go"}, "stage": {"loadingPage": "Loading Page", "viewports": "Viewports", "themes": "Themes", "frameWidth": "frame width", "theme": "Design draft mode", "generateSuccess": "Generation Success", "generateFailed": "Generation Failure"}}, "browser": {"title": "No capture information received yet", "help": "Connect the plugin with your browser", "notice": "Capture web pages directly from your browser and import them into Figma at any time. \r\n\nThis browser plugin is perfect for private or local pages, logged-in pages, session states, removing banner ads, etc. \r\n\nIt works with Chrome, Edge, Brave, Arc, Opera, Vivaldi and Yandex browsers.", "btn": "Install Browser Plugin"}, "text": {"input": {"title": "Enter description to generate UI design", "help": "Describe your project, and AI will help you.", "placeholder": "Please enter description"}, "btn": "Redirect Plugin Usage"}, "image": {"importTip": "Drag and drop or paste here", "selectLayer": "select from layer", "require": {"fileType": "Supported formats", "resolution": "Maximum Resolution", "fileSize": "Maximum file size"}, "sizeLimit": "Image size cannot exceed 5MB", "stage": {"generateSuccess": "Page Generation Success"}}}, "edit": {"title": "MagicEdit", "desc": "AI Assisted Image Replacement Search", "placeholder": ""}, "faq": {"title": "FAQ"}, "feedback": {"title": "Thank you for your feedback 😍", "inputTip": "Please complete this document to help us improve.", "placeholder": "Add a comment", "submit": "Submission of comments", "comments": "comments"}, "stage": {"stop": "Stop", "back": "Back", "question": "How do you feel about the generated results?"}, "upload": {"running": "Uploading", "done": "Done", "failed": "Failed", "dragDropText": "Drop files to start parsing", "draggingText": "Drag and drop here", "normal": "Click to upload"}, "footer": {"version": "Version Information: V", "contact": "Contact", "agreement": "Agreement"}, "settings": {"logout": "Log out", "visit": "Visit the official website.", "contact": "Contact US"}, "common": {"examples": "Examples", "more": "more", "pleaseInput": "Please enter your question", "placeholder": "please enter content...", "referenceOnly": "The content is generated by AI macromodels and is for informational purposes only.", "suggestSuffix": "Press Tab to apply suggestions"}}