{"name": "@ai-assitant/d2c2d-demo-ui", "version": "0.0.1", "description": "", "type": "module", "scripts": {"dev": "vite", "build": "pnpm build:before && pnpm build:figma-plugin && vite build", "build:dev": "pnpm build:before && pnpm build:figma-plugin && vite build --mode development --watch", "build:test": "pnpm build:before && pnpm build:figma-plugin && vite build --mode test", "build:before": "rm -rf dist && pnpm --filter @tencent/design-ai-utils build && pnpm --filter @tencent/design-to-code build", "build:figma-plugin": "pnpm --filter @ai-assitant/ai-figma build && pnpm --filter @ai-assitant/ai-figma build:d2c2d-plugin", "test": "vitest", "tscheck": "tsc --noEmit", "lint": "eslint . --fix"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.9.0", "@stylistic/eslint-plugin": "^4.2.0", "autoprefixer": "^10.4.20", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "less": "^4.2.0", "simple-zustand-devtools": "^1.1.0", "tailwindcss": "^3.4.15", "vite-plugin-generate-file": "^0.2.0", "vite-plugin-svg-icons": "^2.0.1"}, "dependencies": {"@ai-assitant/ai-bridge": "workspace:*", "@ai-assitant/ai-core": "workspace:*", "@ai-assitant/ai-figma": "workspace:*", "@tencent/h2d-html-parser": "workspace:*", "@tencent/design-ai-utils": "workspace:*", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "@tanstack/react-virtual": "^3.13.2", "classnames": "^2.5.1", "clsx": "^2.1.1", "framer-motion": "^12.4.7", "i18next": "^23.15.2", "immer": "^10.1.1", "inferencejs": "^1.0.21", "js-beautify": "^1.15.4", "react-i18next": "^15.1.1", "socket.io-client": "^4.8.1", "swr": "^2.3.3", "tailwind-merge": "2.6.0", "tdesign-icons-react": "^0.4.2", "tdesign-react": "^1.10.0", "zustand": "^4.5.4"}}