import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

import enUS from './en_US.json';
import zhCN from './zh_CN.json';

const resources = {
  'en-US': {
    translation: enUS,
  },
  'zh-CN': {
    translation: zhCN,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: 'zh-CN',
  interpolation: {
    escapeValue: false,
  },
});

export enum Locales {
  EN_US = 'en-US',
  ZH_CN = 'zh-CN',
}

type LocaleOptions = {
  label: string;
  value: Locales;
}[];

export const localeOptions: LocaleOptions = [
  {
    label: 'English',
    value: Locales.EN_US,
  },
  {
    label: '简体中文',
    value: Locales.ZH_CN,
  },
];

export default i18n;
