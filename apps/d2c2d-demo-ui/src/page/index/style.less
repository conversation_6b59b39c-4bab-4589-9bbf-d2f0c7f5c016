.pg-index {
  // 选项卡
  &__tabs.t-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .t-tabs__header {
      flex-shrink: 0;
    }
    .t-tabs__content {
      flex: 1;
    }
    .t-tab-panel {
      height: 100%;
      overflow-y: auto;
    }
  }

  // design to code
  &-d2c {
    padding: 80px 50px 50px;

    &__btns {
      margin-top: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // design to code result
  &-d2c-result {
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;

    .ui-result-panel {
      flex: 1;
      height: 1px;
    }
    &__btns {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
      flex-shrink: 0;

      .btns-main {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
  }

  // ai
  &-ai {
    padding: 80px 50px 50px;
  }
}