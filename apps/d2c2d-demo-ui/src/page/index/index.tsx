import React from 'react';
import Header from '@/components/Header';
import { Layout, LayoutContent } from '@/components/Layout';
import { Tabs } from 'tdesign-react';
import DesignToCode from './components/design-to-code';
import AI from './components/ai';
import './style.less';

const { TabPanel } = Tabs;

export default function Index() {
  return (
    <Layout>
      <LayoutContent>
        <Tabs placement={'top'} size={'medium'} defaultValue={1} className='pg-index__tabs ui-tabs'>
          <TabPanel value={1} label="Design to Code">
            <DesignToCode />
          </TabPanel>
          <TabPanel value={2} label="AI 修改设计稿">
            <AI />
          </TabPanel>
        </Tabs>
      </LayoutContent>
    </Layout>
  );
}