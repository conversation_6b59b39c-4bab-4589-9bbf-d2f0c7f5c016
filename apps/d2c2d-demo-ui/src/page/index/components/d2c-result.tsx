import React, { useEffect, useState } from 'react';
import { Button } from 'tdesign-react';
import ResultPanel from '@/components/result-panel';
import { CODE } from './mockData';
import { GeneratePageCode } from '@/type';
import { MessageTypes } from '@ai-assitant/ai-core';
import { figmaMessages, formatHtmlAsync } from '@/utils';
import CopyButton from '@/components/copy-button';

const mockPageCode = {
  error: {
    success: false,
    error: '请返回上一步，然后选择要转换的设计稿再操作'
  },
  success: {
    success: true,
    code: CODE
  },
  loading: {
    success: null,
    code: '',
  }
};

interface D2CResultProps {
  reset: () => void;
  frames: Array<any>;
}

export default function D2CResult(props: D2CResultProps) {
  const { reset, frames } = props;
  const [pageCode, setPageCode] = useState<GeneratePageCode>(mockPageCode.loading);

  const designToCode = async () => {
    const data = (await figmaMessages[MessageTypes.D2C2D_DEMO].request({
      task: 'design-to-code',
      nodes: frames,
    }) as any).data;
    setPageCode({
      success: true,
      code: await formatHtmlAsync(data?.htmlString),
    });
  };

  const handleDownloadCode = () => {
    const a = document.createElement('a');
    a.href =
      'data:text/plain;charset=utf-8,' +
      encodeURIComponent(pageCode?.success ? (pageCode?.code ?? '') : '');
    a.download = `d2c.html`;
    a.click();
  };

  useEffect(() => {
    if (!frames?.length) {
      setPageCode(mockPageCode.error);
    } else {
      designToCode();
    }
  }, [frames]);

  console.log('pageCode', pageCode);

  return (
    <div className='pg-index-d2c-result'>
      <ResultPanel 
        pageCode={pageCode}
      />
      <div className="pg-index-d2c-result__btns">
        <Button theme='primary' variant='outline' className='ui-btn' onClick={reset}>返回上一步</Button>
        <div className="btns-main">
          <Button theme='primary' className='ui-btn' onClick={handleDownloadCode}>下载代码</Button>
          <CopyButton 
            disabled={!pageCode?.code} 
            content={pageCode?.success ? (pageCode?.code ?? '') : ''}
          />
        </div>
      </div>
    </div>
  );
};