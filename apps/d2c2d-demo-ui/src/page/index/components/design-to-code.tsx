import React, { useCallback, useEffect, useState } from 'react';
import TipsBox from '@/components/tips-box';
import { Button } from 'tdesign-react';
import D2CResult from './d2c-result';
import { useSelection } from '@/hooks';

export default function DesignToCode() {
  const [frames, setFrames] = useState<Array<any>>([]);
  const [isStar, setIsStar] = useState(false);
  const { selection } = useSelection();
  const hasFrames = frames?.length > 0;
  const reset = useCallback(() => {
    setFrames([]);
    setIsStar(false);
  }, []);
  const start = useCallback(() => {
    setIsStar(true);
  }, []);

  useEffect(() => {
    if (!isStar) {
      setFrames(selection);
    }
  }, [isStar, selection]);

  if (!isStar) {
    return (
      <div className='pg-index-d2c'>
        <TipsBox>{hasFrames ? '已选择设计稿，可以点击下方按钮进行转换！' : '请选择要转换成代码的设计稿！'}</TipsBox>
        <div className="pg-index-d2c__btns">
          <Button theme='primary' className='ui-btn' disabled={!hasFrames} onClick={start}>开始转换</Button>
        </div>
      </div>
    );
  }

  return <D2CResult reset={reset} frames={frames} />;
};