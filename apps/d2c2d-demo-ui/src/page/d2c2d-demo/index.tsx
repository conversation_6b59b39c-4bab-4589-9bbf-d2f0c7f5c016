import './index.less';

import { MessageTypes } from '@ai-assitant/ai-core';
import { useState, useContext, useEffect, useMemo } from 'react';
import { Button, Textarea, Progress, Tabs } from 'tdesign-react';

import { HtmlPreview } from '@/components';
import { useOptimize } from '@/hooks'
import { figmaMessages } from '@/utils';
import htmlParser, { htmlUpdate } from '@/utils/html-parser';
import mockHtml from './mock-html';
import { AppContext } from '@/context';
import { OptimizeTaskMessage } from '@/type';

const { TabPanel } = Tabs;


export default function D2c2dDemo() {
  const [htmlObject, setHtmlObject] = useState<any>(null);
  const [htmlString, setHtmlString] = useState<any>(null);
  const [familyMap, setFamilyMap] = useState<any>(null);

  const appContext = useContext(AppContext);
  useEffect(() => {
    appContext?.$socket.connect();
  }, []);

  const {
    optimizePrompt,
    setOptimizePrompt,
    requestOptimizeLoading,
    requestOptimize,
    cancelOptimize,
    requestOptimizeTask,
    taskId,
  } = useOptimize();

  const initFamilyMap = async () => {
    // 初步要先把 figma 中的字体列表给收集好
    console.log('familyMap', familyMap);
    if (!familyMap) {
      const result = await figmaMessages[MessageTypes.GET_FONT_LIST].request({});
      setFamilyMap(result);
    }
  };



  const designToCode = async () => {
    const data = (await figmaMessages[MessageTypes.D2C2D_DEMO].request({
      task: 'design-to-code',
    }) as any).data;
    setHtmlObject(data?.htmlObject);
    setHtmlString(data?.htmlString);
    return data;
  };

  const htmlStringToObject = async () => {
    const codeData = await designToCode();

    const data = (await figmaMessages[MessageTypes.D2C2D_DEMO].request({
      task: 'html-string-to-object',
      htmlString: codeData.htmlString,
    }) as any).data;
    console.log('codeData.htmlString', codeData.htmlString);
    console.log('data', data);
  };

  const codeToDesign = async () => {
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();

    const codeData = await designToCode();
    console.log('nodeId', codeData?.htmlObject?.[0]?.nodeId);
    await htmlParser.create({
      html: codeData.htmlString,
      name: 'test',
      pageParams: {
        viewport: 1920,
        theme: 'light',
        isUseAutoLayout: true,
        withoutHtmlBody: true,
        container: {
          targetId: codeData?.htmlObject?.[0]?.nodeId,
          optType: 'modify',
        }
      }
    }, (data) => {
      if (data.status === "Error") {
        console.log(data.error.message || data.error);
      }
    });
  }
  const d2c2dTest = async (chain?: boolean) => {
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();

    console.log('1、开始从设计稿中获取 html');
    const codeData = await designToCode();
    console.log('2、ai 修改 html');
    const html = (await requestOptimize(codeData.htmlString, chain)).replace('```html', '').replace('```', '');
    console.log('ai 修改后的 html', html);
    setHtmlString(html);
    console.log('3、将修改后的 html 回填到设计稿中');
    await htmlParser.create({
      html: html,
      name: 'test',
      pageParams: {
        viewport: 1920,
        theme: 'light',
        isUseAutoLayout: true,
        withoutHtmlBody: true,
        container: {
          targetId: codeData?.htmlObject?.[0]?.nodeId,
          optType: 'modify',
        }
      }
    }, (data) => {
      if (data.status === "Error") {
        console.log(data.error.message || data.error);
      }
    });
  };

  const testMock = async () => {
    if (!mockHtml) {
      return;
    }
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();

    setHtmlString(mockHtml);
    await htmlParser.create({
      html: mockHtml,
      name: 'test',
      pageParams: {
        viewport: 1920,
        theme: 'light',
        isUseAutoLayout: true,
        withoutHtmlBody: true,
      }
    }, (data) => {
      if (data.status === "Error") {
        console.log(data.error.message || data.error);
      }
    });
  };



  const optimizeHtml = async (chain?: boolean) => {
    const html = await requestOptimize(htmlString, chain);
    console.log('html', html);
    setHtmlString(html);
  }


  const codeToDesignUpdate = async () => {
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();

    const codeData = await designToCode();
    const nodeId = codeData?.htmlObject?.[0]?.nodeId;
    console.log('nodeId', codeData?.htmlObject?.[0]?.nodeId);
    console.log('htmlString', codeData?.htmlString)

    htmlUpdate({
      html: codeData.htmlString,
      name: 'test',
      css: "body{margin:0}",
      pageParams: {
        viewport: 1920,
        theme: 'light',
        isUseAutoLayout: true,
        withoutHtmlBody: true,
      }
    }, nodeId)
  }

  const codeToDesignAIUpdate = async () => {
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();

    console.log('1、开始从设计稿中获取 html');
    const codeData = await designToCode();
    const nodeId = codeData?.htmlObject?.[0]?.nodeId;
    console.log('nodeId', codeData?.htmlObject?.[0]?.nodeId);

    console.log('2、ai 修改 html');
    const html = (await requestOptimize(codeData.htmlString, true)).replace('```html', '').replace('```', '');
    console.log('ai 修改后的 html', html);
    setHtmlString(html);
    console.log('3、将修改后的 html 回填到设计稿中');

    htmlUpdate({
      html,
      name: 'test',
      css: "body{margin:0}",
      pageParams: {
        viewport: 1920,
        theme: 'light',
        isUseAutoLayout: true,
        withoutHtmlBody: true,
      }
    }, nodeId)
  };


  const codeToDesignUpdateMock = async () => {
    if (!mockHtml) {
      return;
    }
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();

    setHtmlString(mockHtml);
    const nodeId = '2164:157'
    htmlUpdate({
      html: mockHtml,
      name: 'test',
      css: "body{margin:0}",
      pageParams: {
        viewport: 1920,
        theme: 'light',
        isUseAutoLayout: true,
        withoutHtmlBody: true,
      }
    }, nodeId)
  }


  const [taskInfo, setTaskInfo] = useState<{
    total: number;
    completed: number;
  } | null>(null);

  const taskPercentage = useMemo(() => {
    if (!taskInfo) return 0
    return ~~(taskInfo.completed / taskInfo.total) * 100
  }, [taskInfo])

  useEffect(() => {
    appContext?.$socket.onMessage(async (message: OptimizeTaskMessage) => {
      console.log(message)

      if (message.taskId !== taskId.current) return


      if (message.status === 'error') {
        console.error(message.error)
        return
      }

      if (message.status === 'running') {
        const { newContent, nodeId, parentNodeId, action } = message.data

        await htmlUpdate({
          html: newContent,
          name: 'test',
          css: "body{margin:0}",
          pageParams: {
            viewport: 1920,
            theme: 'light',
            isUseAutoLayout: true,
            withoutHtmlBody: true,
          }
        }, nodeId)

        if (message.completed === message.total) {
          setTaskInfo(null)
          return
        }

        setTaskInfo({
          total: message.total,
          completed: message.completed
        })
      }
    });
  }, []);
  const codeToDesignUpdateTask = async () => {
    // 在 code to design 之前先将可用字体收集好
    await initFamilyMap();


    try {
      console.log('1、开始从设计稿中获取 html');
      const codeData = await designToCode();
      console.log('2、提交任务');
      await requestOptimizeTask(codeData.htmlString);
      console.log('taskId', taskId)
    } catch (e) {
      setTaskInfo(null)
      throw e
    }
  }



  return (
    <Tabs className='ui-tabs' placement={'top'} size={'medium'} defaultValue={1}>
      <TabPanel value={1} label="默认" className='p-3'>
        <Button className='ui-btn' onClick={designToCode}>design to code</Button>
        <Button className='ui-btn' onClick={htmlStringToObject}>htmlString to htmlObject</Button>
        <Button className='ui-btn' onClick={codeToDesign}>code to design</Button>
        <Button className='ui-btn' onClick={testMock}>测试 code to design 的一些特殊场景</Button>

        <div className="">
          <Textarea
            className='ui-textarea'
            value={optimizePrompt}
            onChange={(value) => {
              setOptimizePrompt(value);
            }}
            placeholder="请输入内容, 如：文本变为大写，颜色变为红色，字体变为微软雅黑等"
          />
          <div className='flex gap-2 flex-wrap'>
            <Button className='ui-btn ml-4' onClick={() => optimizeHtml()} loading={requestOptimizeLoading}>修改 code</Button>
            <Button className='ui-btn ml-4' onClick={() => optimizeHtml(true)} loading={requestOptimizeLoading}>修改 code chain</Button>
            <Button className='ui-btn' onClick={() => d2c2dTest()}>测试整个完整流程</Button>
            <Button className='ui-btn' onClick={() => d2c2dTest(true)}>chain测试整个完整流程</Button>


          </div>
        </div>
        <HtmlPreview html={htmlString} />
      </TabPanel>

      <TabPanel value={2} label="增量" className='p-3'>
        <div className='flex mt-2 space-x-2 flex-wrap gap-2'>
          <Button className='ui-btn' onClick={codeToDesignUpdate}>D2CC2D 还原</Button>
          <Button className='ui-btn' onClick={codeToDesignUpdateMock}>D2CC2D 还原 Mock</Button>

          <Button className='ui-btn' onClick={codeToDesignAIUpdate}>D2CC2D AI</Button>
          <Button className='ui-btn ml-4' disabled={!!taskInfo} onClick={codeToDesignUpdateTask}>
            {taskInfo ?
              <Progress percentage={taskPercentage}></Progress>
              : 'D2CC2D AI 任务'
            }
          </Button>

        </div>

        <div className="">
          <Textarea
            className='ui-textarea'
            value={optimizePrompt}
            onChange={(value) => {
              setOptimizePrompt(value);
            }}
            placeholder="请输入内容, 如：文本变为大写，颜色变为红色，字体变为微软雅黑等"
          />
        </div>


        <HtmlPreview html={htmlString} />
      </TabPanel>
    </Tabs>

  );
}