import './index.less';

import { useState } from 'react';
import { Button, Form, Select, Tabs } from 'tdesign-react';

import HtmlPreview from '@/components/HtmlPreview';
import Preview from '@/components/Preview';
import { useDetect } from '@/hooks';

const { FormItem } = Form;
const { TabPanel } = Tabs;

export default function DetectDemo() {
  const {
    isLoading,
    setIsLoading,
    predictionsList,
    componentNodesList,
    html,
    getPredictions,
    getComponentNodesList,
    getHtml,
  } = useDetect();

  const [type, setType] = useState('button');
  const onChange = (value: string) => {
    setType(value);
  };

  console.log('predictionsList', predictionsList);
  console.log('componentNodesList', componentNodesList);
  console.log('html', html);

  return (
    <div className="pg-detect-demo">
      <Form labelAlign="right" layout="vertical" preventSubmitDefault resetType="empty" showErrorMessage>
        <FormItem initialData="button" label="识别的组件类型:" name="type">
          <Select
            value={type}
            onChange={onChange as any}
            options={[
              { label: '按钮', value: 'button' },
              { label: '其他', value: 'other' },
            ]}
          />
        </FormItem>
        <FormItem>
          <Button
            onClick={async () => {
              setIsLoading(true);
              await getPredictions();
              setIsLoading(false);
            }}
            loading={isLoading}
          >
            1、设计稿中识别组件位置
          </Button>
          <Button
            onClick={async () => {
              setIsLoading(true);
              await getComponentNodesList(true);
              setIsLoading(false);
            }}
            loading={isLoading}
          >
            2、设计稿中提取出图层信息
          </Button>
          <Button
            onClick={async () => {
              setIsLoading(true);
              await getHtml(true);
              setIsLoading(false);
            }}
            loading={isLoading}
          >
            3、设计稿中提取出组件 html
          </Button>
        </FormItem>
      </Form>
      {predictionsList?.length > 0 && (
        <Tabs placement="top" size="medium" defaultValue={1}>
          {predictionsList.map((item, index) => {
            return (
              <TabPanel key={index} value={index + 1} label={item?.node?.id}>
                <Preview data={item} />
              </TabPanel>
            );
          })}
        </Tabs>
      )}
      {html?.length > 0 && (
        <Tabs placement="top" size="medium" defaultValue={1}>
          {html.map((item: any, index: number) => {
            const htmlString = item.componentNodes.map((cNode: any) => cNode.html).join('');

            return (
              <TabPanel key={index} value={index + 1} label={item?.node?.id}>
                <HtmlPreview html={htmlString} />
              </TabPanel>
            );
          })}
        </Tabs>
      )}
    </div>
  );
}
