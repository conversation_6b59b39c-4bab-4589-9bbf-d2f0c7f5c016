import 'tdesign-react/esm/style/index.js';
import '@/style/global.less';
import '@/style/tdesign-reset.less';

import { useEffect, useState, useContext } from 'react';

import { ResizeWindow } from '@/components';
import { RoutePath } from '@/type';

import { AppContext } from '../context';
import { useEventEmitter } from 'ahooks';
import { useSocket } from '../hooks';

import Index from './index';
import D2c2dDemo from './d2c2d-demo';
import DetectDemoPage from './detect-demo';

export default function PageRoot() {
  const [currentRoute, setCurrentRoute] = useState<RoutePath>('index');


  const renderPage = () => {
    switch (currentRoute) {
      case 'index':
        return <Index />;
      case 'd2c2d-demo':
        return <D2c2dDemo />;
      case 'detect-demo':
        return <DetectDemoPage />;
    }
  };


  return (
    <>
      <AppContext.Provider
        value={{
          $focusInput: useEventEmitter(),
          $retry: useEventEmitter(),
          $upload: useEventEmitter(),
          $socket: useSocket(),
        }}>
        {renderPage()}
      </AppContext.Provider>
      <ResizeWindow />
    </>
  );
}
