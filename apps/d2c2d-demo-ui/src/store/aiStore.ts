import { mountStoreDevtool } from 'simple-zustand-devtools';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { DesignComponents, DesignStyleKeyword, PageArchitecture, PageType, UseImageContent } from '@/type';

export interface AIStore {
  /** 用户初次输入的设计需求，首页聊天框的prompt */
  prompt: string;
  designStyleKeywords: DesignStyleKeyword[];
  designStylePrompt: string;
  pageType: PageType;
  pageArchitecture: PageArchitecture;
  /** 记录页面选择状态 */
  pageSelectMap: Record<string, boolean>;
  /** 当前会话 Id */
  sessionId?: string;
  /** 是否开启设计组件生成模式 */
  designComponentMode: boolean;
  /** 设计组件数据 */
  designComponents?: DesignComponents;
  /** 记录设计组件的选中状态 */
  designComponentMap: Record<string, boolean>;
  /** 参考图片的参考范围：页面结构、视觉风格、文本内容三种，false时表示不开启参考图片模式 */
  isUseImage: Record<UseImageContent, boolean> | false;
  /** 参考图片数据，base64格式 */
  referenceImage?: string;
}

const useAIStore = create<AIStore>()(
  subscribeWithSelector(
    immer((): AIStore => {
      return {
        prompt: '',
        pageType: PageType.app,
        pageArchitecture: {},
        pageSelectMap: {},
        designComponentMap: {},
        designComponentMode: false,
        designStyleKeywords: [],
        designStylePrompt: '',
        isUseImage: false,
      };
    }),
  ),
);

// useAIStore.subscribe(
//   (newState) => {
//     return newState.prompt;
//   },
//   (prompt) => {
//     window.localStorage.setItem('$ai-gen-design-prompt', prompt);
//   },
// );

if (import.meta.env.DEV) {
  mountStoreDevtool('AIStore', useAIStore);
}

export { useAIStore };
