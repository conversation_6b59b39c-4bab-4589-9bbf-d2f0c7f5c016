import {
  DeepPartial,
  DesignComponentsSchema,
  PageArchitectureWithComponentSchema,
  PageWithComponentSchema,
  SectionWithComponentSchema,
} from '@tencent/design-ai-utils';
import { z } from 'zod';

import { Locales } from './locale';
import { AIStore } from './store/aiStore';

export enum PageType {
  app = 'app',
  web = 'web',
  console = 'console',
}

export enum UseImageContent {
  structure = 'structure',
  style = 'style',
  copywriting = 'copywriting',
}

export enum DesignMode {
  design_component = 'design_component',
  normal = 'normal',
}

export type Section = DeepPartial<z.infer<typeof SectionWithComponentSchema>>;
export type Page = DeepPartial<z.infer<typeof PageWithComponentSchema>>;
export type PageArchitecture = DeepPartial<z.infer<typeof PageArchitectureWithComponentSchema>>;
export type DesignComponents = z.infer<typeof DesignComponentsSchema>;

export interface RequestPageArchitectureBody {
  model?: string;
  prompt?: string;
  pageType?: PageType;
  sessionId?: string;
  updateMeasure?: 'whole' | 'page' | 'section';
  updateId?: string;
  pageArchitecture?: PageArchitecture;
  designStylePrompt?: string;
  designStyleKeywords?: string[];
  mode?: DesignMode;
  designComponents?: any[];
  useImage?: AIStore['isUseImage'];
  referenceImage?: string;
}

export type DesignStyleKeyword = {
  key: string;
  value: Record<Locales, string>;
};

export type GeneratePageCode = {
  success: boolean | null;
  code?: string;
  error?: string;
};

export type RoutePath = 'index' | 'd2c2d-demo' | 'detect-demo';

export interface OptimizeTaskMessage {
  taskId: string;
  status: 'pending' | 'running' | 'success' | 'error';
  total: number;
  completed: number;
  error?: string;
  data?: any;
}
