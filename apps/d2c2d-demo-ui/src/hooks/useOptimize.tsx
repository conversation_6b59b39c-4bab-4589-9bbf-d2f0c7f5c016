import { useCallback, useEffect, useRef, useState, useContext } from 'react';


import { API_PREFIX } from '@/constant';
import { request } from '@/utils';

import { AppContext } from '../context';

export default function useOptimize() {
  const abortController = useRef<AbortController>();
  const [optimizePrompt, setOptimizePrompt] = useState<string>('');
  const [requestOptimizeLoading, setRequestOptimizeLoading] = useState(false);
  const taskId = useRef('')

  const appContext = useContext(AppContext);




  useEffect(() => {
    return () => {
      abortController.current?.abort('suggest abort');
    };
  }, []);

  const requestOptimize = async (htmlStr: string, chain?: boolean) => {
    if (abortController.current) {
      abortController.current.abort('Optimize abort');
    }
    abortController.current = new AbortController();

    try {
      setRequestOptimizeLoading(true);
      const res = await request(`${API_PREFIX}/${chain ? 'optimize-chain' : 'optimize'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: abortController.current.signal,
        body: JSON.stringify({
          prompt: optimizePrompt,
          htmlStr,
        }),
      });
      const data = (await res.json()).data;
      if (data.includes('null')) {
        return undefined;
      }

      return data.replace(/^["''"]|["''"]$/g, '');
    } finally {
      setRequestOptimizeLoading(false);
    }
  }

  const cancelOptimize = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort('Optimize abort');
    }
  }, []);

  const requestOptimizeTask = async (htmlStr: string) => {
    if (abortController.current) {
      abortController.current.abort('Optimize abort');
    }
    abortController.current = new AbortController();

    try {
      setRequestOptimizeLoading(true);
      const res = await request(`${API_PREFIX}/task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: abortController.current.signal,
        body: JSON.stringify({
          prompt: optimizePrompt,
          htmlStr,
          connectionId: appContext?.$socket.socket.id,
        }),
      });
      const data = (await res.json());
      if (data.code !== 0) {
        throw new Error(data.message)
      }
      taskId.current = data.data
    } finally {
      setRequestOptimizeLoading(false);
    }
  }


  return {
    optimizePrompt,
    setOptimizePrompt,
    requestOptimizeLoading,
    requestOptimize,
    cancelOptimize,
    taskId,
    requestOptimizeTask
  };
}
