import { Nullable } from '@ai-assitant/ai-core';
import { useRef } from 'react';
import { Subject, Subscription } from 'rxjs';
import io, { Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';
import { API_SOCKET } from '@/constant';

export class SocketClient {
  socket: Socket;
  userId: string;
  connectionId: string;

  messageSubject = new Subject();

  messageSubscription: Nullable<Subscription> = null;

  constructor(userId: string) {
    this.userId = userId;

    this.connectionId = uuidv4();
    this.socket = io(API_SOCKET, {
      autoConnect: false, // 可选：是否自动连接
      reconnection: true, // 可选：断线后是否重连
      transports: ['websocket'], // 指定传输方式
    });

    this.socket.on('connect', () => {
      console.log('Connected to the server');
      this.socket.emit('start');
    });

    this.socket.on('message', (message) => {
      // console.log('Received message from the server', message);
      this.messageSubject.next(JSON.parse(message));
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from the server');
    });
  }

  connect() {
    this.socket.connect();
  }

  onMessage(listener: (message: any) => void) {
    this.messageSubscription = this.messageSubject.subscribe((message) => {
      listener(message);
    });
  }

  offMessage() {
    this.messageSubscription?.unsubscribe();
  }
}

export default function useSocket() {
  const ref = useRef<SocketClient>();
  if (!ref.current) {
    ref.current = new SocketClient('');
  }
  return ref.current;
}
