import { MessageTypes } from '@ai-assitant/ai-core';
import { useState, useEffect } from 'react';
import { figmaMessages } from '@/utils';
import { selectionEmitter } from '@ai-assitant/ai-figma';

export function useSelection() {
  const [selection, setSelection] = useState<Array<any>>([]);

  // 初始化时获取一次
  useEffect(() => {
    figmaMessages[MessageTypes.GET_SELECTION].request({}).then((result: any) => {
      console.log('result', result);
      if (result?.data) {
        setSelection(result?.data);
      }
    });
  }, []);

  useEffect(() => {
    selectionEmitter.on((selection) => {
      setSelection(selection);
    });

    return () => {
      selectionEmitter.off();
    };
  }, []);

  return {
    selection,
  }
}