import { cn } from '@/utils';

export interface SvgIconProps extends React.SVGProps<SVGSVGElement> {
  name: string;
  prefix?: string;
}

export default function SvgIcon({ name, prefix = 'icon', className, ...props }: SvgIconProps) {
  const symbolId = `#${prefix}-${name}`;
  return (
    <svg className={cn('w-4 h-4', className)} aria-hidden="true" {...props}>
      <use href={symbolId} />
    </svg>
  );
}
