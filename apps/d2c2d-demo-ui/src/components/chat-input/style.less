.ui-chat-input {
  position: relative;
  padding: 16px;
  align-items: center;
  box-sizing: border-box;
  border-radius: 8px;
  background: #292B29;
  border: 1px solid rgba(255, 255, 255, 0.12);

  &:before {
    content: '';
    position: absolute;
    right: 0;
    bottom: 100%;
    z-index: 1;
    margin-bottom: -10px;
    width: 89px;
    height: 56px;
    background: url('./img/avatar.png') no-repeat center center;
    background-size: contain;
    pointer-events: none;
  }

  &__btns {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 10px;
  }

  &__textarea {
    --td-text-color-primary: #fff;
    --td-text-color-placeholder: rgba(255, 255, 255, 0.7);

    .t-textarea__inner,
    .t-textarea__inner:focus {
      padding: 0;
      background: transparent;
      border-width: 0;
      box-shadow: none;
    }
  }

  &__alert.t-alert {
    margin-bottom: 40px;
  }
}