import './style.less';

import { MessageTypes } from '@ai-assitant/ai-core';
import React, { useState } from 'react';
import { PlayIcon } from 'tdesign-icons-react';
import { Button,Textarea,Alert,MessagePlugin } from 'tdesign-react';

import { useOptimize } from '@/hooks'
import { figmaMessages } from '@/utils';
import { htmlUpdate } from '@/utils/html-parser';
import { useSelection } from '@/hooks';

export default function ChatInput() {
  const [familyMap, setFamilyMap] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { selection } = useSelection();
  const hasSelection = selection?.length > 0;

  const {
    optimizePrompt,
    setOptimizePrompt,
    requestOptimize,
  } = useOptimize();

  const initFamilyMap = async () => {
    // 初步要先把 figma 中的字体列表给收集好
    console.log('familyMap', familyMap);
    if (!familyMap) {
      const result = await figmaMessages[MessageTypes.GET_FONT_LIST].request({});
      setFamilyMap(result);
    }
  };

  const designToCode = async () => {
    const data = (await figmaMessages[MessageTypes.D2C2D_DEMO].request({
      task: 'design-to-code',
      nodes: selection,
    }) as any).data;
    return data;
  };

  const codeToDesignAIUpdate = async () => {
    try {
      setIsLoading(true);
      // 在 code to design 之前先将可用字体收集好
      await initFamilyMap();
  
      console.log('1、开始从设计稿中获取 html');
      const codeData = await designToCode();
      const nodeId = codeData?.htmlObject?.[0]?.nodeId;
      console.log('nodeId', codeData?.htmlObject?.[0]?.nodeId);
  
      console.log('2、ai 修改 html');
      const html = (await requestOptimize(codeData.htmlString, true)).replace('```html', '').replace('```', '');
      console.log('ai 修改后的 html', html);

      console.log('3、将修改后的 html 回填到设计稿中');
      await htmlUpdate({
        html,
        name: 'test',
        css: "body{margin:0}",
        pageParams: {
          viewport: 1920,
          theme: 'light',
          isUseAutoLayout: true,
          withoutHtmlBody: true,
        }
      }, nodeId);
      setIsLoading(false);
      MessagePlugin.success({
        content: '修改成功',
        className: 'h-8',
        offset: [0, -15],
      });
    } catch (error) {
      console.error(error);
      setIsLoading(false);
      MessagePlugin.error({
        content: '修改失败',
        className: 'h-8',
        offset: [0, -15],
      });
    }
  };

  const disabled = isLoading || optimizePrompt === '' || !hasSelection;

  return (
    <>
      <Alert
        theme={hasSelection ? "success" : "info"} 
        message={hasSelection ? '已选择设计稿，可以输入要修改的内容进行调整' : "请选择要修改的设计稿"} 
        className="ui-alert ui-chat-input__alert"
      />
      <div className="ui-chat-input">
        <Textarea 
          className='ui-chat-input__textarea' 
          value={optimizePrompt}
          onChange={(value) => {
            setOptimizePrompt(value);
          }}
          placeholder="请输入内容, 如：文本变为大写，颜色变为红色，字体变为微软雅黑等"
          rows={4} 
        />
        <div className="ui-chat-input__btns">
          <Button className='ui-btn' shape="circle" icon={<PlayIcon />} onClick={codeToDesignAIUpdate} disabled={disabled} loading={isLoading} />
        </div>
      </div>
    </>
  );
}