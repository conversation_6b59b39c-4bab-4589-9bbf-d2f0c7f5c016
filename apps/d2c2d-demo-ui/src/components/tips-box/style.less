.ui-tips-box {
  position: relative;
  display: flex;
  min-height: 150px;
  padding: 20px 16px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  border-radius: 8px;
  background: #292B29;
  border: 1px solid rgba(255, 255, 255, 0.12);

  &:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: 100%;
    z-index: 1;
    margin-bottom: -10px;
    width: 89px;
    height: 56px;
    background: url('./img/avatar.png') no-repeat center center;
    background-size: contain;
  }
}