import { useState } from 'react';
import { CopyIcon } from 'tdesign-icons-react';
import { Button, ButtonProps, MessagePlugin } from 'tdesign-react';

export default function CopyButton({
  content,
  onDone,
  onError,
  ...restProps
}: {
  content: string | ClipboardItem | ClipboardItem[] | (() => string | ClipboardItem | ClipboardItem[]);
  onDone?: () => void;
  onError?: (err: any) => void;
} & ButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyPolyfill = (content: string) => {
    return new Promise<void>((resolve, reject) => {
      const textarea = document.createElement('textarea');
      textarea.value = content;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      textarea.setSelectionRange(0, textarea.value.length);
      const success = document.execCommand('copy');
      document.body.removeChild(textarea);
      if (success) resolve();
      else reject();
    });
  };

  const handleCopy = () => {
    let promise;
    const realContent = typeof content === 'function' ? content() : content;
    if (typeof realContent === 'string') {
      if (navigator.clipboard) {
        promise = navigator.clipboard.writeText(realContent);
      } else {
        promise = handleCopyPolyfill(realContent);
      }
    } else if (navigator.clipboard) {
      // @ts-ignore
      promise = navigator.clipboard.write(Array.isArray(realContent) ? realContent : [realContent]);
    }
    promise
      ?.then(() => {
        onDone?.();
      })
      .catch((err) => {
        onError?.(err);
      })
      .finally(() => {
        MessagePlugin.success({
          content: '复制成功',
          className: 'h-8',
          offset: [0, -15],
        });
      });
  };

  return (
    <Button className='ui-btn' icon={<CopyIcon />} onClick={handleCopy} {...restProps} />
  );
}
