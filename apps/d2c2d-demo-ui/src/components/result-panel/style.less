.ui-result-panel {
  display: flex;
  justify-content: space-between;
  height: 100%;
  position: relative;

  &__preview {
    position: relative;
    width: 59%;
    min-width: 420px;
    border-radius: 2px;
    background: #EBEBF0;
    overflow: hidden;
  }
  &__code {
    width: 40%;
    height: 100%;
    min-width: 340px;
    border-radius: 2px;
    overflow: hidden;
  }

  &__btns {
    position: absolute;
    right: 0;
    top: 0;
    transition: all .3s linear;
    opacity: 0;
    display: flex;
    align-items: center;
    gap: 8px;

    .ui-btn {
      padding: 0;
      min-width: 32px;
    }
  }
  &:hover &__btns {
    opacity: 1;
  }

  // is-full
  &.is-full &__preview {
    width: 100%;
  }
  &.is-full &__code {
    display: none;
  }
}

.ui-preview-frame {
  position: relative;
  height: 100%;
  overflow: hidden;

  &__status {
    @apply bg-gray-50;
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;

    &-inner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 8px;
    }
    &-text {
      @apply text-red-500 text-xs;
    }
  }
  &__main {
    position: relative;
    background: white;
    height: 100%
  }
  &__bar {
    @apply bg-gray-800 text-white text-xs space-x-2;
    height: 40px;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;
    align-items: center;

    &-btns {
      @apply space-x-1;
      display: flex;
      align-items: center;
    }
    &-btn {
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
    &-title {
      @apply bg-gray-700 text-gray-300 truncate;
      flex: 1;
      padding-left: 12px;
      padding-right: 12px;
      padding-top: 4px;
      padding-bottom: 4px;
      text-align: center;
    }
  }
  &__iframe {
    min-height: 0;
    width: 100%;
    height: calc(100% - 40px);
  }
}