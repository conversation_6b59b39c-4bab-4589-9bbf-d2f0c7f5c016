import { GeneratePageCode } from '@/type';
import CodeEditor from './CodeEditor';
import PreviewFrame from './PreviewFrame';
import React, { useState } from 'react';
import { Button } from 'tdesign-react';
import { Fullscreen1Icon, FullscreenExitIcon, } from 'tdesign-icons-react';
import clsx from 'clsx';
import './style.less';

export default function ResultPanel({
  pageCode,
}: {
  pageCode: GeneratePageCode;
}) {
  const [isFull, setIsFull] = useState(false);

  return (
    <div className={clsx("ui-result-panel", { 'is-full': isFull})}>
      <div className="ui-result-panel__preview">
        <PreviewFrame pageCode={pageCode} isFull={isFull} />
        <div className="ui-result-panel__btns">
          <Button 
            className='ui-btn ui-result-panel__toggle' 
            icon={isFull ? <FullscreenExitIcon /> : <Fullscreen1Icon />} 
            onClick={() => setIsFull(!isFull)} 
          />
          {/* <Button 
            className='ui-btn' 
            icon={<ZoomOutIcon />} 
          />
          <Button 
            className='ui-btn' 
            icon={<ZoomInIcon />} 
          /> */}
        </div>
      </div>
      <div className="ui-result-panel__code">
        <CodeEditor pageCode={pageCode} />
      </div>
    </div>
  );
}
