import { useEventListener } from 'ahooks';
import { useEffect, useRef, useState } from 'react';

import failedGif from '@/assets/failed.gif';
import loadingGif from '@/assets/loading.gif';
import successGif from '@/assets/success.gif';
import { GeneratePageCode } from './interface';
import { cn } from '@/utils';

const WebWidth = 1920;

function formatDesignComponentElement(code: string) {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(code, 'text/html');

    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      return code;
    }

    const presetClass = 'bg-gray-100';
    const dcElements = doc.querySelectorAll('[dc="1"]');

    dcElements.forEach((element) => {
      element.innerHTML = '';
      if (element.hasAttribute('class')) {
        const classValue = element.getAttribute('class') || '';
        if (!classValue.includes(presetClass)) {
          element.setAttribute('class', `${classValue} ${presetClass}`.replace(/\s+/g, ' ').trim());
        }
      } else {
        element.setAttribute('class', presetClass);
      }
    });

    const serializer = new XMLSerializer();
    return serializer.serializeToString(doc);
  } catch (error) {
    // 如果有任何错误，直接返回原始代码
    console.error('Error processing HTML:', error);
    return code;
  }
}

export default function PreviewFrame({ pageCode, isFull = false }: { pageCode: GeneratePageCode, isFull?: boolean }) {
  const [tempUrl, setTempUrl] = useState<string>('');
  const [isFrameLoading, setIsFrameLoading] = useState(true);
  const [iframeWrapperStyle, setIframeWrapperStyle] = useState<React.CSSProperties>({});
  const iframeWrapperRef = useRef<HTMLDivElement>(null);

  const notReady = pageCode.success == null;
  const isFailed = pageCode.success === false;

  const computeIframeWrapperStyle = () => {
    if (!iframeWrapperRef.current) return;
    const wrapperWidth = iframeWrapperRef.current.clientWidth;
    const wrapperHeight = iframeWrapperRef.current.clientHeight;

    const scale = wrapperWidth / WebWidth;
    setIframeWrapperStyle({
      width: WebWidth,
      height: wrapperHeight / scale,
      transform: `scale(${scale})`,
      transformOrigin: 'top left',
    });
  };

  useEventListener('resize', computeIframeWrapperStyle, { target: window });

  useEffect(() => {
    computeIframeWrapperStyle();
    setIsFrameLoading(true);
    let code = pageCode.code ?? '';

    code = formatDesignComponentElement(code);

    const blob = new Blob([code], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    setTempUrl(url);

    return () => {
      URL.revokeObjectURL(url);
    };
  }, [pageCode]);

  useEffect(() => {
    if (isFull) {
      setIframeWrapperStyle({});
    } else {
      computeIframeWrapperStyle();
    }
  }, [isFull]);

  return (
    <div ref={iframeWrapperRef} className="ui-preview-frame">
      {isFailed && (
        <div className="ui-preview-frame__status">
          <div className="ui-preview-frame__status-inner">
            <img src={failedGif} className="size-40" />
            <span className="ui-preview-frame__status-text">{pageCode.error}</span>
          </div>
        </div>
      )}

      {(isFrameLoading || notReady) && (
        <div className="ui-preview-frame__status">
          <img src={notReady ? loadingGif : successGif} className="size-48" />
        </div>
      )}

      <div className="ui-preview-frame__main" style={iframeWrapperStyle}>
        <div className="ui-preview-frame__bar">
          <div className="ui-preview-frame__bar-btns">
            <div className="ui-preview-frame__bar-btn bg-red-500"></div>
            <div className="ui-preview-frame__bar-btn bg-yellow-500"></div>
            <div className="ui-preview-frame__bar-btn bg-green-500"></div>
          </div>
          <div className="ui-preview-frame__bar-title">
            example.com
          </div>
        </div>
        <iframe
          src={tempUrl}
          className={cn('ui-preview-frame__iframe')}
          sandbox="allow-scripts"
          onLoad={() => setIsFrameLoading(false)}
          style={{
            border: 'none',
            overflow: 'hidden',
          }}
        />
      </div>
    </div>
  );
}
