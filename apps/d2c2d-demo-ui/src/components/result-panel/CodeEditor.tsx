import { Editor } from '@monaco-editor/react';
import { useMemo } from 'react';
import { GeneratePageCode } from '@/type';

export default function CodeEditor({ pageCode }: { pageCode: GeneratePageCode }) {
  const notReady = pageCode.success == null;

  const codeEditorValue = useMemo(() => {
    if (!pageCode?.success || !pageCode.code) return '';
    let filteredCode = pageCode.code;
    filteredCode = filteredCode.replace(/<img([^>]*)src=(["'])data:image[^"']*\2([^>]*)>/gi, '<img$1$3>');
    return filteredCode;
  }, [pageCode]);

  return (
    <div className="size-full relative">
      {notReady && (
        <div className="absolute inset-0 size-full p-3 z-10 bg-primaryBg">
          <div className="w-4/5 space-y-4">
            <div className="h-4 bg-white/20 rounded-sm animate-pulse"></div>
            <div className="h-4 bg-white/20 rounded-sm w-2/3 animate-pulse"></div>
            <div className="h-4 bg-white/20 rounded-sm animate-pulse"></div>
          </div>
        </div>
      )}
      <Editor
        loading="loading..."
        defaultLanguage="html"
        options={{
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          lineDecorationsWidth: 0,
          folding: true,
          renderLineHighlight: 'all',
        }}
        theme="vs-dark"
        value={codeEditorValue}
      />
    </div>
  );
}
