import './style.less';

import React from 'react';

interface PreviewProps {
  data: any;
}
export default function Preview(props: PreviewProps) {
  const { data } = props;

  return (
    <div className="ui-preview">
      <img src={data?.screenshot} />
      {data?.predictions?.map((item: any, index: number) => {
        const width = item?.bbox?.width;
        const height = item?.bbox?.height;
        const left = item?.bbox?.x - width / 2;
        const top = item?.bbox?.y - height / 2;
        return (
          <div
            key={index}
            className="ui-preview__mark"
            style={{
              width: width,
              height: height,
              left: left,
              top: top,
            }}
          >
            <span className="ui-preview__mark-bg" style={{ backgroundColor: item.color }}></span>
            <span className="ui-preview__mark-text">{item.class}</span>
          </div>
        );
      })}
    </div>
  );
}
