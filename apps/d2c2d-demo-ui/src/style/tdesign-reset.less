:root {
  --td-text-color-secondary: #fff;
  --td-text-color-primary: #fff;
  --td-brand-color: #61F29F;
  --td-brand-color-hover: #7CE8AA;
  --td-brand-color-active: #1FF279;
  --td-component-stroke: #414445;
  --td-bg-color-container-hover: #292B29;
}

.ui-btn {
  --td-text-color-anti: #000000;

  &.t-button--variant-outline {
    --td-bg-color-specialcomponent: transparent;
  }
}

.ui-alert {
  --td-brand-color-focus: #1E1E1E;

  &.t-alert {
    box-shadow: 0px 6px 30px 5px rgba(255, 255, 255, 0.05), 0px 16px 24px 2px rgba(255, 255, 255, 0.04), 0px 8px 10px -5px rgba(255, 255, 255, 0.08);
  }
  &.t-alert--success {
    --td-text-color-secondary: #000;
  }
}

.ui-tabs.t-tabs {
  background: transparent;
}

.ui-textarea {
  --td-text-color-primary: #fff;
  --td-text-color-placeholder: rgba(255, 255, 255, 0.7);

  .t-textarea__inner {
    background: #292B29;
    border: 1px solid rgba(255, 255, 255, 0.12);
      box-shadow: none;
  }
}