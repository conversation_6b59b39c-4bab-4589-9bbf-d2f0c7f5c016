@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    background: #1F2025;
  }
}

@layer utilities {
  .custom-scrollbar {
    &::-webkit-scrollbar {
      @apply w-1;
      @apply h-1;
    }
    &::-webkit-scrollbar-track {
      @apply bg-transparent rounded-lg;
    }
    &::-webkit-scrollbar-thumb {
      @apply bg-primary/50 rounded-lg;
    }
    &::-webkit-scrollbar-corner {
      @apply bg-transparent;
    }
  }

  .loading-shimmer {
    @apply relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/40 before:to-transparent before:animate-[shimmer_1.5s_infinite];
  }

  .loading-opacity {
    @apply before:content-[''] before:absolute before:left-0 before:top-0 before:w-full before:h-full before:bg-black/50 before:z-50 before:animate-opacity pointer-events-none;
  }
}

.t-message__list {
  .t-message {
    @apply border shadow-2xl;
    &.t-is-error {
      @apply bg-red-500/20 text-red-500 border-red-500;
      svg {
        @apply text-red-500 !important;
      }
    }

    &.t-is-info {
      @apply bg-blue-500/20 text-blue-500 border-blue-500;
      svg {
        @apply text-blue-500 !important;
      }
    }

    &.t-is-success {
      @apply bg-primary/20 text-primary border-primary;
      svg {
        @apply text-primary !important;
      }
    }
    &.t-is-loading {
      @apply bg-primary/20 text-primary border-primary;
      svg {
        @apply text-primary !important;
      }
    }
  }
}

.t-notification {
  @apply shadow-2xl !important;
  @apply bg-primaryBg !important;

  &.t-notification-is-error {
    svg {
      @apply text-red-500 !important;
    }
    .t-notification__title__wrap,
    .t-notification__title,
    .t-notification__content {
      @apply text-red-500;
    }

    .t-icon-close {
      transition: all 0.2s ease-in-out;
      &:hover {
        background: transparent;
        transform: scale(1.5);
      }
    }
  }

  &.t-notification-is-info {
    svg {
      @apply text-primary !important;
    }
    .t-notification__title__wrap,
    .t-notification__title,
    .t-notification__content {
      @apply text-primary;
    }

    .t-icon-close {
      transition: all 0.2s ease-in-out;
      &:hover {
        background: transparent;
        transform: scale(1.5);
      }
    }
  }
}

.t-switch {
  &.t-is-checked {
    @apply bg-primary !important;
  }
}
