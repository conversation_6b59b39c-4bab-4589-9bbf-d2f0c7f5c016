import { Locales } from '@/locale';

import { DesignStyleKeyword } from './type';

export const API_PREFIX = import.meta.env.VITE_PLUGIN_SERVER_URL + '/html-optimize';

export const API_SOCKET = import.meta.env.VITE_PLUGIN_SOCKET_URL;

export const DesignStyleKeywords: DesignStyleKeyword[] = [
  { key: 'light', value: { [Locales.EN_US]: 'light', [Locales.ZH_CN]: '浅色' } },
  { key: 'dark', value: { [Locales.EN_US]: 'dark', [Locales.ZH_CN]: '暗色' } },
  { key: 'simple', value: { [Locales.EN_US]: 'simple', [Locales.ZH_CN]: '简约' } },
  { key: 'corporate', value: { [Locales.EN_US]: 'corporate', [Locales.ZH_CN]: '企业' } },
  { key: 'imitation', value: { [Locales.EN_US]: 'imitation', [Locales.ZH_CN]: '拟态' } },
  { key: 'artistic', value: { [Locales.EN_US]: 'artistic', [Locales.ZH_CN]: '艺术' } },
  { key: 'tech', value: { [Locales.EN_US]: 'tech', [Locales.ZH_CN]: '科技感' } },
  { key: 'active', value: { [Locales.EN_US]: 'active', [Locales.ZH_CN]: '活泼的' } },
  { key: 'fashion', value: { [Locales.EN_US]: 'fashion', [Locales.ZH_CN]: '时尚的' } },
];

export const APP_WIDTH = 412;
export const WEB_WIDTH = 1920;

export const MESSAGE_PLUGIN_CONFIG = {
  className: 'h-8',
  offset: [0, -15],
};
export const ClientStoryKeys = {
  uid: 'AI_GEN_DESIGN_UI_UID',
};

export const VERSION = '1.0.0';
