import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';

interface SseRequestOptions {
  method?: 'POST' | 'GET';
  body?: Record<string, any>;
  headers?: Record<string, any>;
  signal?: AbortSignal;
  onMessage?: (e: EventSourceMessage) => void;
  onClose?: () => void;
  onError?: (err: any, fromMessage: boolean) => void;
}

export function sseRequest(url: string, options: SseRequestOptions = {}) {
  return fetchEventSource(url, {
    method: options.method ?? 'POST',
    body: JSON.stringify(options.body),
    headers: { 'Content-Type': 'application/json', ...options.headers },
    openWhenHidden: true,
    signal: options.signal,
    onerror: (error) => {
      options.onError?.(error.message, false);
      throw error;
    },
    onmessage: (msg: EventSourceMessage) => {
      if (msg.data === '') return;
      options.onMessage?.(msg);

      if (msg.event === 'error') {
        options.onError?.(msg.data, true);
      }
    },
    onclose: options.onClose,
  });
}
