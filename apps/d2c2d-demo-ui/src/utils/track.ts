// 文档：https://iwiki.woa.com/p/564086851
import { MessageTypes } from '@ai-assitant/ai-core';

// import Aegis from "aegis-web-sdk";
import { ClientStoryKeys, VERSION } from '@/constant';
import { figmaMessages } from '@/utils';

class RequestQueue {
  queue: Array<string>;
  constructor() {
    this.queue = [];
  }

  async addRequest(url: string) {
    this.queue.push(url);
    await this.processQueue();
  }

  async processQueue() {
    if (this.queue.length === 0) {
      return;
    }

    const url: string = this.queue.shift() as string;

    try {
      await fetch(url, {
        method: 'GET',
      });
    } catch (error) {
      console.error('Error:', error);
    }
    await this.processQueue();
  }
}

class Track {
  //   private script_id: string = "horizon-analytics";
  private collect_uri: string
    = 'https://ap-guangzhou.cls.tencentcs.com/track?topic_id=ec98919a-f46a-40a6-b004-6d90fae54cce';

  private version: string = '1.0.0';
  private domain: string = import.meta.env.VITE_TRACK_DOMAIN;
  //   private event: string = "";
  private figmaCurrentUser: any = null;
  private uid: string = '';
  private cookie_key_uid = '_horizon_uid';
  private eventCategory: string | null = null;
  private eventAction: string | null = null;
  private eventLabel: string | null = null;
  private eventValue: string | null = null;
  private requestQueueFetch = new RequestQueue();
  private utm_source: string = '';
  private extra_info: string | null = null;

  constructor() {}

  //   private aegis(): Aegis {
  //     const _aegis = new Aegis({
  //       id: "bRLKOT6Rql4GW9p1XG",
  //       version: "1.0.0",
  //       uin: "",
  //       spa: true,
  //       reportApiSpeed: true,
  //       reportAssetSpeed: true,
  //     });
  //     return _aegis;
  //   }

  private platform(): string {
    return navigator.userAgent;
  }

  /**
   * @title 整理请求的 URL
   */
  private async url(event: string) {
    const uid = await this.getUid();
    const userId = await this.getUserId();
    const extra_info = await this.getExtraInfo();
    // const account = CONFIG.HORIZON_ID;
    let url = this.collect_uri;
    url += `&v=${this.version}`;
    url += `&a=${import.meta.env.VITE_TRACK_ENV}`; // 开发或生产环境判断
    url += `&t=${event}`;
    url += `&referer=${this.platform()}`; // 操作系统
    url += `&uid=${uid}`; // 客户端的 uid，即插件只要未重启，则一直使用这个 uid
    url += `&ts=${this.timestamp()}`;
    url += `&user_id=${userId}`; // 用户id 目前没有登录，暂以 figma 用户 id 代替
    url += `&d=${this.domain}`; // 来源类型判断，即是 web 端还是各类插件端
    url += `&utm_source=${this.utm_source}`; // 当前站点的域名
    url += `&et=${extra_info}`; // 记录 figma 的用户信息、插件版本号等
    if (this.eventCategory !== null) url += `&ec=${this.eventCategory}`;
    if (this.eventAction !== null) url += `&ea=${this.eventAction}`;
    if (this.eventLabel !== null) url += `&el=${this.eventLabel}`;
    if (this.eventValue !== null) url += `&ev=${this.eventValue}`;
    return url;
  }

  private async getFigmaCurrentUser(): Promise<any> {
    if (this.figmaCurrentUser) {
      return this.figmaCurrentUser;
    }

    this.figmaCurrentUser = ((await figmaMessages[MessageTypes.GET_PLATFORM_USER].request({})) as any)?.data;
    return this.figmaCurrentUser;
  }

  private async getUid(): Promise<string> {
    const uid = ((await figmaMessages[MessageTypes.GET_STORAGE].request({ key: ClientStoryKeys.uid })) as any)?.data;

    if (!uid) {
      this.uid = this.genUUID();
      await figmaMessages[MessageTypes.SET_STORAGE].send({ key: ClientStoryKeys.uid, value: this.uid });
    } else {
      this.uid = uid;
    }
    return this.uid;
  }

  private async getUserId() {
    const figmaCurrentUser = await this.getFigmaCurrentUser();
    if (figmaCurrentUser) {
      return figmaCurrentUser.userId;
    }

    return '';
  }

  private removeBrackets(str: string) {
    // 当前系统不支持 json 字符串，需要把字符串前后的花括号去掉才可以录入，否则系统会忽略这次上报
    return encodeURI(str.replace(/(^\{)|(\}$)/g, ''));
  }

  private async getExtraInfo() {
    if (this.extra_info !== null) {
      return this.extra_info;
    }

    const figmaCurrentUser = await this.getFigmaCurrentUser();
    let extra_info = {
      version: VERSION,
      figmaUserName: figmaCurrentUser?.userName || '',
    } as any;
    extra_info = this.removeBrackets(JSON.stringify(extra_info));
    this.extra_info = extra_info;
    return this.extra_info;
  }

  /**
   *
   * @returns {number}
   */
  private timestamp() {
    return new Date().getTime();
  }

  private doSend(url: string) {
    // 为了复用请求连接，保证同一时间只发出一个请求
    this.requestQueueFetch.addRequest(url);
  }

  /**
   * 生成 uuid xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
   * @returns {string}
   */
  private genUUID() {
    let uuid = '';
    let ii;
    for (ii = 0; ii < 32; ii += 1) {
      switch (ii) {
        case 8:
        case 20:
          uuid += '-';
          uuid += ((Math.random() * 16) | 0).toString(16);
          break;
        case 12:
          uuid += '-';
          uuid += '4';
          break;
        case 16:
          uuid += '-';
          uuid += ((Math.random() * 4) | 8).toString(16);
          break;
        default:
          uuid += ((Math.random() * 16) | 0).toString(16);
      }
    }
    return uuid;
  }

  private getEventCategoryByPlatform(eventCategory: string) {
    if (!eventCategory) {
      return null;
    }
    if (eventCategory.indexOf('figma') < 0) {
      return `figma.${eventCategory}`;
    }
    return eventCategory;
  }

  private getEventActionByPlatform(eventAction: string) {
    if (!eventAction) {
      return null;
    }
    if (eventAction.indexOf('figma') < 0) {
      return `figma.${eventAction}`;
    }
    return eventAction;
  }

  private async $send(eventCategory = '', eventAction = '', eventLabel = '', eventValue?: object) {
    const event = 'event';
    this.eventCategory = this.getEventCategoryByPlatform(eventCategory);
    this.eventAction = this.getEventActionByPlatform(eventAction);
    this.eventLabel = eventLabel;
    this.eventValue = eventValue ? this.removeBrackets(JSON.stringify(eventValue)) : '';
    const collectUrl = await this.url(event);
    if (collectUrl) this.doSend(collectUrl);
  }

  /** 上报 pv/uv */
  async sendPV(router: string = '') {
    const event = 'pageview';
    this.utm_source = router;
    const collectUrl = await this.url(event);
    if (collectUrl) this.doSend(collectUrl);
  }

  /**
   * 主动上报数据
   * track.send('main.go-next-step'); // 用于记录点击次数
   * track.send("menu.generate", { label: "label.sum.generate" }); // label 可以用于两级标识，当这个点击行为需要区分不同的情况时，可以加这个
   * track.send('create.failed', { eventValue: JSON.stringify(error) }); // eventValue 用于添加一些额外的信息，比如错误信息
   */
  async send(
    action: string,
    value: {
      category?: string;
      label?: string;
      eventValue?: object;
    } = {},
  ) {
    const { category = '', label = '', eventValue } = value;
    this.$send(category, action, label, eventValue);
  }
}

const track = new Track();

export default track;
