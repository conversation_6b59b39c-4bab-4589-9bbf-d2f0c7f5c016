interface XMLNode {
  tag: string;
  attributes: Record<string, string>;
  text: string;
  children: XMLNode[];
  parent: XMLNode | null;
}

export class StreamXMLParser {
  private buffer: string = '';
  private currentNode: XMLNode | null = null;
  private root: XMLNode | null = null;
  private isParsing: boolean = false;
  private workList: string[] = [];
  private onParse: (result: any | null) => void;

  constructor(onParse: (result: any | null) => void) {
    this.onParse = onParse;
    this.reset();
  }

  reset() {
    this.buffer = '';
    this.currentNode = null;
    this.isParsing = false;
    this.workList = [];
    this.root = null;
  }

  feed(chunk: string) {
    if (!chunk) return;

    this.workList.push(chunk);
    if (!this.isParsing) {
      this.doWork();
    }
  }

  private async doWork() {
    if (this.workList.length === 0 || this.isParsing) return;

    const chunk = this.workList.shift();
    this.buffer += chunk;
    this.isParsing = true;

    this.parse();
    this.tryGenerateResult();

    this.isParsing = false;
    this.doWork();
  }

  private parse() {
    while (this.buffer.length > 0) {
      if (this.buffer.startsWith('<') && !this.buffer.startsWith('</')) {
        const endIndex = this.buffer.indexOf('>');
        if (endIndex === -1) return;

        const tagContent = this.buffer.substring(1, endIndex);
        const [tagName, ...attrParts] = tagContent.split(' ');

        // 解析所有属性
        const attributes: Record<string, string> = {};
        attrParts.forEach((attr) => {
          const match = attr.match(/([^=]+)="([^"]+)"/);
          if (match) {
            const [, key, value] = match;
            attributes[key] = value;
          }
        });

        const newNode: XMLNode = {
          tag: tagName,
          attributes,
          text: '',
          children: [],
          parent: this.currentNode,
        };

        if (this.currentNode) {
          this.currentNode.children.push(newNode);
        } else {
          this.root = newNode;
        }

        this.currentNode = newNode;
        this.buffer = this.buffer.substring(endIndex + 1);
        continue;
      }

      // 查找结束标签
      if (this.buffer.startsWith('</')) {
        const endIndex = this.buffer.indexOf('>');
        if (endIndex === -1) return; // 标签不完整，等待更多数据

        const tagName = this.buffer.substring(2, endIndex);
        if (this.currentNode && this.currentNode.tag === tagName) {
          this.currentNode = this.currentNode.parent;
        }

        this.buffer = this.buffer.substring(endIndex + 1);
        continue;
      }

      // 处理文本内容
      const nextTagIndex = this.buffer.indexOf('<');
      if (nextTagIndex === -1) {
        if (this.currentNode) {
          this.currentNode.text += this.buffer;
        }
        this.buffer = '';
      } else {
        if (this.currentNode) {
          this.currentNode.text += this.buffer.substring(0, nextTagIndex);
        }
        this.buffer = this.buffer.substring(nextTagIndex);
      }
    }
  }

  private convertNodeToJSON(node: XMLNode): any {
    const result: any = {};

    // 如果有属性，添加到结果对象中
    if (Object.keys(node.attributes).length > 0) {
      Object.assign(result, node.attributes);
    }

    // 如果只有文本内容且没有属性，直接返回文本
    if (node.children.length === 0) {
      return Object.keys(result).length > 0 ? { ...result, value: node.text } : node.text;
    }

    // 处理子节点
    const childrenByTag: Record<string, XMLNode[]> = {};

    // 先收集所有同名标签
    for (const child of node.children) {
      if (!(child.tag in childrenByTag)) {
        childrenByTag[child.tag] = [];
      }
      childrenByTag[child.tag].push(child);
    }

    // 处理每个标签组
    for (const [tag, children] of Object.entries(childrenByTag)) {
      if (children.length > 1) {
        // 多个同名标签，使用 tag_id 作为key
        for (const child of children) {
          const id = child.attributes.id;
          if (!id) {
            throw new Error(`Multiple ${tag} tags found without id attribute`);
          }
          const key = `${tag}_${id}`;
          result[key] = this.convertNodeToJSON(child);
        }
      } else {
        // 单个标签，使用驼峰命名的标签名作为key
        const key = this.toCamelCase(tag);
        result[key] = this.convertNodeToJSON(children[0]);
      }
    }

    return result;
  }

  private tryGenerateResult() {
    if (!this.root) {
      this.onParse(null);
      return;
    }

    try {
      const result = this.convertNodeToJSON(this.root);
      JSON.stringify(result); // 验证是否为合法JSON
      this.onParse(result);
    } catch {
      this.onParse(null);
    }
  }

  private toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, g => g[1].toUpperCase());
  }
}
