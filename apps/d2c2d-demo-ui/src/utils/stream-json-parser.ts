export class StreamJSONParser {
  buffer: string = '';
  isComplete: boolean = false;
  workList: string[] = [];
  parsing: boolean = false;

  constructor(
    private onParse?: (res: any, isComplete: boolean) => void,
    private onError?: (err: any) => void,
  ) {}

  feed(chunk: string) {
    try {
      if (this.isComplete) {
        throw new Error('JSON 已解析完毕，不会再继续处理数据，请 reset 后重新开始');
      }
      if (typeof chunk !== 'string') {
        chunk = chunk + '';
      }
      if (chunk.length === 0) {
        return;
      }
      this.workList.push(chunk);

      if (!this.parsing) {
        this.tryParseJSON();
      }
    } catch (err) {
      this.onError?.(err);
    }
  }

  reset() {
    this.buffer = '';
    this.isComplete = false;
    this.parsing = false;
    this.workList = [];
  }

  private tryParseJSON() {
    try {
      this.parsing = true;
      this.buffer += this.workList.shift();
      this.buffer = this.buffer.trim();
      if (!this.buffer) {
        return;
      }

      // 找到第一个有效的 JSON 左括号
      const startIndex = this.buffer.search(/[{[]/);
      if (startIndex === -1) {
        return;
      }

      // 找到有效括号，删除前面的内容
      if (startIndex > 0) {
        this.buffer = this.buffer.slice(startIndex);
      }

      // 找到最后一个右括号的位置
      const lastCloseBrace = this.buffer.lastIndexOf('}');
      const lastCloseBracket = this.buffer.lastIndexOf(']');
      const lastClosePos = Math.max(lastCloseBrace, lastCloseBracket);

      if (lastClosePos === -1) {
        const value = this.parsePartialJSON();
        if (value !== undefined) {
          this.onParse?.(value, false);
        }
        return;
      }

      try {
        const value = JSON.parse(this.buffer.slice(0, lastClosePos + 1));
        this.isComplete = true;
        this.onParse?.(value, true);
      } catch {
        // 失败说明JSON还不完整，解析部分数据
        const value = this.parsePartialJSON();
        if (value !== undefined) {
          this.onParse?.(value, false);
        }
      }
    } finally {
      this.parsing = false;
      if (this.workList.length > 0) this.tryParseJSON();
    }
  }

  private parsePartialJSON(): any {
    const jsonString = this.buffer;
    if (!jsonString) {
      return;
    }

    const length = jsonString.length;
    let index = 0;

    const throwError = (msg: string) => {
      throw new Error(`${msg}（位置：${index}）`);
    };

    const parseAny: () => any = () => {
      skipBlank();
      if (index >= length) throwError('JSON 数据意外结束');

      if (jsonString[index] === '"') return parseStr();
      if (jsonString[index] === '{') return parseObj();
      if (jsonString[index] === '[') return parseArr();
      return parseNum();
    };

    const parseStr: () => string = () => {
      const start = index;
      let escape = false;
      index++; // 跳过左引号
      while (index < length && (jsonString[index] !== '"' || (escape && jsonString[index - 1] === '\\'))) {
        escape = jsonString[index] === '\\' ? !escape : false;
        index++;
      }
      if (jsonString.charAt(index) == '"') {
        try {
          return JSON.parse(jsonString.substring(start, ++index - Number(escape)));
        } catch {
          throwError('字符串格式错误');
        }
      } else {
        try {
          return JSON.parse(jsonString.substring(start, index - Number(escape)) + '"');
        } catch {
          // 无效的转义
          return JSON.parse(jsonString.substring(start, jsonString.lastIndexOf('\\')) + '"');
        }
      }
      throwError('字符串未正确结束');
    };

    const parseObj = () => {
      index++; // 跳过左大括号
      skipBlank();
      const obj: Record<string, any> = {};
      try {
        while (jsonString[index] !== '}') {
          skipBlank();
          if (index >= length) return obj;
          const key = parseStr();
          skipBlank();
          index++; // 跳过冒号
          try {
            const value = parseAny();
            obj[key] = value;
          } catch {
            return obj;
          }
          skipBlank();
          if (jsonString[index] === ',') index++; // 跳过逗号
        }
      } catch {
        return obj;
      }
      index++; // 跳过右大括号
      return obj;
    };

    const parseArr = () => {
      index++; // 跳过左括号
      const arr = [];
      try {
        while (jsonString[index] !== ']') {
          arr.push(parseAny());
          skipBlank();
          if (jsonString[index] === ',') {
            index++; //  跳过逗号
          }
        }
      } catch {
        return arr;
      }
      index++; // 跳过右括号
      return arr;
    };

    const parseNum = () => {
      if (index === 0) {
        if (jsonString === '-') throwError('无效的负号"-"');
        try {
          return JSON.parse(jsonString);
        } catch {
          return JSON.parse(jsonString.substring(0, jsonString.lastIndexOf('e')));
        }
      }

      const start = index;

      if (jsonString[index] === '-') index++;
      while (jsonString[index] && ',]}'.indexOf(jsonString[index]) === -1) index++;

      try {
        return JSON.parse(jsonString.substring(start, index));
      } catch {
        if (jsonString.substring(start, index) === '-') throwError('无效的负号"-"');
        try {
          return JSON.parse(jsonString.substring(start, jsonString.lastIndexOf('e')));
        } catch {
          throwError('数字格式错误');
        }
      }
    };

    const skipBlank = () => {
      while (index < length && ' \n\r\t'.includes(jsonString[index])) {
        index++;
      }
    };

    return parseAny();
  }
}
