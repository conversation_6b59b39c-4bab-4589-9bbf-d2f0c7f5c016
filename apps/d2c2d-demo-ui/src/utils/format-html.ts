import beautify from 'js-beautify';

const htmlTemplate = `
<!DOCTYPE html>
<html lang="zh">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, maximum-scale=1">
	<meta charset="utf-8">
	<title>页面</title>
	<meta name="format-detection" content="telephone=no">
</head>
<body>
{content}
</body>
</html>
`;

export async function formatHtmlAsync(content: string) {
  if (!content || typeof content !== 'string') {
    return '';
  }
  return beautify.html(htmlTemplate.replace('{content}', content), {
    indent_size: 2,
    space_in_empty_paren: true,
    // 保留 span（文本节点） 中的空白
    content_unformatted: ['span', 'pre'],
  });
}
