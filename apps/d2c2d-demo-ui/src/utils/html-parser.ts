import { FigmaResult } from '@ai-assitant/ai-core';
import { HtmlParser, IhtmlParams, ImageHashInfo } from '@tencent/h2d-html-parser';

import { figmaMessages } from './figma';

const htmlParser = new HtmlParser({
  apiUrl: import.meta.env.VITE_PLUGIN_SERVER_URL,
  source: 'figma',
  getImageHash: (args) => {
    return figmaMessages['get-image-hash'].request(args) as Promise<FigmaResult<ImageHashInfo>>;
  },
  getVideoHash: (args) => {
    return figmaMessages['get-video-hash'].request(args) as Promise<FigmaResult<string>>;
  },
  createDesign: (args) => {
    return figmaMessages['create-design'].request(args) as Promise<string>;
  },
  createDesignCollect: (isCreateAssets) => {
    figmaMessages['create-design-collect'].send({ isCreateAssets });
  },
});

export async function htmlUpdate(params: IhtmlParams, id: string) {
  const dsl = await htmlParser.getDSL(params);
  if (dsl) {
    const args = {
      model: dsl.dslObj,
      id,
      data: {
        name: params.name,
        mappingFamily: dsl.mappingFamily,
      },
    };
    return figmaMessages['update-design'].request(args) as Promise<string>;
  }
}

export default htmlParser;
