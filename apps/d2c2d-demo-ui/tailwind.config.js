/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx,less}'],
  theme: {
    extend: {
      colors: {
        primary: '#9AD600',
        primaryBorder: '#444445', // 主要元素的边框色
        primaryBg: '#323232', // 主要元素的背景色
        rootBg: '#1F2025', // 用于页面背景色
      },
      keyframes: {
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        opacity: {
          '0%': { opacity: 1 },
          '50%': { opacity: 0.5 },
          '100%': { opacity: 1 },
        },
      },
      animation: {
        shimmer: 'shimmer 1.5s infinite',
        opacity: 'opacity 2s ease-in-out infinite',
      },
    },
  },
  plugins: [],
};
