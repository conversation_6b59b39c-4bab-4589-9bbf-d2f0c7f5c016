import { AI_GEN_DESIGN_URL_PREFIX } from '@/constant';
import { PageArchitecture, PageType } from '@/type';

import { request } from './request';

export interface GenerateCodeRequestBody {
  targetPageId: string;
  previousGeneratedCodes: string[];
  pageType: PageType;
  pageArchitecture: PageArchitecture;
  architectureMessageId: string;
  context: Record<string, any>;
}
export async function generateCode(body: GenerateCodeRequestBody, abortController?: AbortController) {
  return request(`${AI_GEN_DESIGN_URL_PREFIX}/code/generate`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: body,
    signal: abortController?.signal,
  });
}

export async function stopGenerateCode(architectureMessageId: string) {
  return request(`${AI_GEN_DESIGN_URL_PREFIX}/code/stop`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: { architectureMessageId },
  });
}
