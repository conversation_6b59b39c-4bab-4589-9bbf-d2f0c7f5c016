import { useState } from 'react';
import useSWR from 'swr';
import useSWRInfinite from 'swr/infinite';
import useSWRMutation from 'swr/mutation';

import { AI_GEN_DESIGN_URL_PREFIX } from '@/constant';
import { IMessageItem4Render, IRating } from '@/type';

import { request } from './request';

/**
 * chat message反馈
 * @param messageId
 * @param successCallback - 成功回调
 * @returns
 */
export function useMessageFeedbackSWR(messageId: string) {
  const swrRes = useSWRMutation(
    `/chat-session/feedback`,
    (url, { arg }: { arg: { messageId: string; rating: IRating } }) => {
      return request(url, {
        method: 'POST',
        body: { messageId: arg.messageId, type: arg.rating },
      });
    },
  );
  return {
    ...swrRes,
    trigger: (rating: IRating) => {
      return swrRes.trigger({ rating, messageId });
    },
  };
}

export interface SessionItem {
  id: string;
  title: string;
  sub_title: string;
  updated_at: string;
}
/**
 * 获取聊天会话列表
 */
export function useSessionListSWR(params?: {
  title?: string; // 模糊搜索值，搜索会话标题
  pageSize?: number; // 每页数量，默认10条
  pageNum?: number; // 页码，默认第1页
  orderBy?: string; // 排序字段，默认按创建时间
  order?: 'asc' | 'desc'; // 排序方式，默认降序
}) {
  return useSWRInfinite<{
    list: SessionItem[];
    count: number;
    hasMore: boolean;
  }>(
    (pageIndex, previousPageData) => {
      if (previousPageData && !previousPageData.hasMore) return null;

      const searchParams = new URLSearchParams({
        // 服务端页码从1开始
        pageNum: (pageIndex + 1).toString(),
        pageSize: params?.pageSize?.toString() ?? '10',
        orderBy: params?.orderBy ?? 'updated_at',
        order: params?.order ?? 'desc',
        chatType: 'text_to_ui',
      });
      if (params?.title) {
        searchParams.set('title', params.title);
      }

      return `/chat-session/list?${searchParams.toString()}`;
    },
    async (url) => {
      const res = await (await request(url)).json();
      return res?.data ?? { list: [], count: 0, hasMore: false };
    },
    {
      revalidateOnFocus: false,
    },
  );
}

/**
 * 获取聊天会话历史消息
 */
export function useHistoryMessagesSWR(sessionId?: string) {
  const [historyMessages, setHistoryMessages] = useState<IMessageItem4Render[]>([]);

  const swrRes = useSWRMutation(
    sessionId ? `${AI_GEN_DESIGN_URL_PREFIX}/messages?sessionId=${sessionId}` : null,
    async (url) => {
      const result = await (await request(url)).json();
      const convertData: IMessageItem4Render[] =
        result?.data?.map((item: any) => {
          return {
            id: item.id,
            status: item.status === 2 ? 'error' : 'success',
            error: item.status === 2 ? item.content : '',
            content:
              item.thinking_enabled === 0
                ? `<think>\n${item.thinking_content}\n</think>\n${item.content}`
                : item.content,
            thinkContent: item.thinking_content,
            isHistory: true,
            metadata: {
              sessionId,
              messageId: item.id,
              parentId: item.parent_id,
            },
            role: item.role === 'USER' ? 'user' : 'ai',
            feedback: item.feedback_type,
            architecture: item.architecture,
          };
        }) ?? [];
      setHistoryMessages(convertData);
      return convertData;
    },
  );

  return {
    historyMessages,
    requestHistoryMessages: swrRes.trigger,
    setHistoryMessages,
    isRequesting: swrRes.isMutating,
  };
}

/**
 * 检查architecture是否已经生成过代码，已生成过不可以重新生成，只可以查看代码
 * @param architectureMessageId
 * @returns
 */
export function useCheckAlreadyGenerateSWR(architectureMessageId?: string) {
  return useSWR(
    architectureMessageId
      ? `${AI_GEN_DESIGN_URL_PREFIX}/architecture/already-generate-check?architectureMessageId=${architectureMessageId}`
      : null,
    async (url) => {
      const res = await (await request(url)).json();
      return res?.data ?? false;
    },
    {
      revalidateOnFocus: false,
    },
  );
}
