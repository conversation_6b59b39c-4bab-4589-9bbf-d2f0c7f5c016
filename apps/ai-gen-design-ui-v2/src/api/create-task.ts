import { MessagePlugin } from 'tdesign-react';

import { request } from '@/api';
import { AI_GEN_DESIGN_URL_PREFIX, MESSAGE_PLUGIN_CONFIG } from '@/constant';
import { PageType } from '@/type';

export async function createTask(pageType: PageType) {
  const res = await request(`${AI_GEN_DESIGN_URL_PREFIX}/create-task`, {
    method: 'POST',
    body: {
      pageType,
    },
  });
  const resObj = await res.json();
  const taskSessionId = resObj.data?.task_session_id;
  if (taskSessionId) {
    return { taskSessionId };
  } else {
    MessagePlugin.error({ content: '创建会话失败', ...MESSAGE_PLUGIN_CONFIG });
    return null;
  }
}
