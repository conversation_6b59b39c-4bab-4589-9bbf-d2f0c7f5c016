import { createSSEParser } from '@tencent/design-ai-utils';

import { API_PREFIX, HEADERS_USER, TRACK_ACTION } from '@/constant';
import { getUser, track } from '@/utils';

export interface SseRequestCallbacks {
  onError?: (error: any) => void;
  onMessage?: (data: any) => void;
  onComplete?: () => void;
}

export type RequestOptions = Omit<RequestInit, 'body'> & { body?: Record<string, any> };

export async function sseRequest(url: string, options: RequestOptions = {}, callbacks?: SseRequestCallbacks) {
  const headers = {
    'Content-Type': 'application/json',
    [HEADERS_USER]: (await getUser()).userId,
    ...options.headers,
  };
  const res = await request(url, {
    method: options.method ?? 'POST',
    body: options.body,
    headers,
    signal: options.signal,
  });

  if (!res?.ok) {
    callbacks?.onError?.(new Error(res.statusText));
    return;
  }

  const sseReadableStream = res.body?.pipeThrough(new TextDecoderStream()).pipeThrough(createSSEParser());
  const reader = sseReadableStream?.getReader();

  while (reader) {
    const { value, done } = await reader.read();
    if (done) {
      callbacks?.onComplete?.();
      break;
    }
    if (!value) continue;
    if (value.event === 'error') {
      callbacks?.onError?.(value.data);
    }
    if (value.data) {
      callbacks?.onMessage?.(value.data);
    }
  }
}

export async function request(url: string, options: RequestOptions = {}) {
  track.send(TRACK_ACTION['request-backend'], {
    eventValue: { url, method: options.method ?? 'POST' },
  });
  const headers = {
    'Content-Type': 'application/json',
    [HEADERS_USER]: (await getUser()).userId,
    ...options.headers,
  };
  const response = fetch(`${API_PREFIX}${url}`, {
    ...options,
    headers,
    body: options.body ? JSON.stringify(options.body) : undefined,
  });
  return response;
}
