import { AI_GEN_DESIGN_URL_PREFIX } from '@/constant';
import { PageArchitecture, PageType } from '@/type';

import { request, sseRequest, SseRequestCallbacks } from './request';

export function manualUpdateArchitecture(params: {
  sessionId: string;
  messageId: string;
  pageArchitecture: PageArchitecture;
}) {
  return request(`${AI_GEN_DESIGN_URL_PREFIX}/architecture/manual-update`, {
    method: 'POST',
    body: params,
  });
}

export function aiUpdateArchitecture(
  params: {
    sessionId: string;
    messageId: string;
    pageArchitecture: PageArchitecture;
    updateMeasure?: 'whole' | 'page' | 'section';
    updateId?: string;
    pageType?: PageType;
    context: Record<string, any>;
  },
  callbacks?: SseRequestCallbacks,
) {
  return sseRequest(
    `${AI_GEN_DESIGN_URL_PREFIX}/architecture/ai-update`,
    {
      method: 'POST',
      body: params,
    },
    callbacks,
  );
}
