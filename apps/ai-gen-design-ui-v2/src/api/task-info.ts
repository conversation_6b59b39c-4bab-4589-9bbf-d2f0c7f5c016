import { AI_GEN_DESIGN_URL_PREFIX } from '@/constant';
import { PageType } from '@/type';

import { request } from './request';

export async function requestTaskInfo(sessionId: string) {
  const res = await (await request(`${AI_GEN_DESIGN_URL_PREFIX}/task?sessionId=${sessionId}`)).json();
  return {
    taskId: res?.data?.task_session_id,
    pageType: res?.data?.page_type === 1 ? PageType.app : PageType.pc,
    image: res?.data?.image,
  };
}
