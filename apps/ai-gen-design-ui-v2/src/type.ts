import { MessageStatus } from '@ant-design/x/es/use-x-chat';
import {
  DeepPartial,
  DesignComponentsSchema,
  PageArchitectureWithComponentSchema,
  PageWithComponentSchema,
  SectionWithComponentSchema,
  TemplateHtmlListSchema,
} from '@tencent/design-ai-utils';
import { z } from 'zod';

import { Locales } from './locale';

export enum PageType {
  app = 'app',
  pc = 'pc',
  // console = 'console',
}

export enum ReferImageContent {
  structure = 'structure',
  style = 'style',
  copywriting = 'copywriting',
}

export type Section = DeepPartial<z.infer<typeof SectionWithComponentSchema>>;
export type Page = DeepPartial<z.infer<typeof PageWithComponentSchema>>;
export type PageArchitecture = DeepPartial<z.infer<typeof PageArchitectureWithComponentSchema>>;
export type DesignComponents = z.infer<typeof DesignComponentsSchema>;
export type TemplateHtmlList = z.infer<typeof TemplateHtmlListSchema>;

type ContextActive = {
  active: boolean;
};

export interface GenerateCodeContext {
  designComponent?: {
    data: any[];
  } & ContextActive;
  templateHtml?: {
    data: TemplateHtmlList;
  } & ContextActive;
}

export type DesignStyleKeyword = {
  key: string;
  value: Record<Locales, string>;
};

export type PageCode = {
  pageName: string;
  pageId: string;
  /**
   * 是否正在生成
   */
  generating: boolean;
  /**
   * 是否已完成生成。不论失败还是成功，都为 true
   */
  completed: boolean;
  codeId?: string;
  code?: string;
  /**
   * 错误信息
   */
  error?: string;
  /**
   * 是否成功生成
   */
  success?: boolean;
};

export type RoutePath = 'main' | 'result' | 'detect-demo';

/**
 * 消息数据对象
 */
export interface IAgentMessage {
  /**
   * 消息元数据
   */
  metadata?: IAgentMessageMetadata;
  /**
   * 思考消息
   */
  thinkContent?: string;
  /**
   * 消息主体内容
   */
  content: string;
}

/**
 * 消息元数据
 */
export interface IAgentMessageMetadata {
  /**
   * 对话 ID
   */
  sessionId: string;
  /**
   * 消息 ID
   */
  messageId: string;
  /**
   * 父消息 ID
   */
  parentId: string;
}

/**
 * 反馈操作类型 like-点赞, dislike-点踩 null-取消
 */
export type IRating = 'like' | 'dislike' | null;

/**
 * 用于渲染的消息数据对象
 */
export interface IMessageItem4Render extends IAgentMessage {
  id: string;
  status: MessageStatus;
  /**
   * 当 status 为 error 时, 返回的错误信息
   */
  error?: string;
  role: 'local' | 'user' | 'ai';
  isHistory?: boolean;
  feedback?: IRating;
  created_at?: string;
  architecture?: PageArchitecture;
}
