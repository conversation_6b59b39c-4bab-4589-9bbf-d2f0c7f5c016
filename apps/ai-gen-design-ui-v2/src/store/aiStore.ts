import { mountStoreDevtool } from 'simple-zustand-devtools';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

import { DesignComponents, PageArchitecture, PageType, ReferImageContent, TemplateHtmlList } from '@/type';
import { filterDesignComponent, getDesignComponentsResourceData } from '@/utils';

export interface AIStore {
  pageType: PageType;
  sessionId?: string;

  referImage: {
    /** 是否开启参考图片模式 */
    active: boolean;
    /** 参考图片数据，cos key */
    data?: string;
    /** 参考图片的cos url */
    url?: string;
    /** 参考图片的参考范围：页面结构、视觉风格、文本内容三种 */
    reference: Record<ReferImageContent, boolean>;
    /** 参考图片的预览数据，base64格式 */
    previewBase64?: string;
    /** 是否正在上传参考图片 */
    uploading?: boolean;
    /** 自定义垫图要求 */
    customizeRequirement?: string;
  };
  designComponent: {
    /** 是否开启设计组件生成模式 */
    active: boolean;
    /** 设计组件数据 */
    data?: DesignComponents;
    /** 记录设计组件的选中状态 */
    designComponentMap: Record<string, boolean>;
  };
  nodeHtml: {
    /** 是否开启基于选中节点生成 */
    active: boolean;
    /** 基于选中节点生成的html数据 */
    data?: TemplateHtmlList;
  };

  getReuqestArchitectureContext: () => Record<string, any>;
  getGenerateCodeContext: (pageArch: PageArchitecture) => Promise<Record<string, any>>;
  resetStore: () => void;
}

const defaultStore: Omit<AIStore, 'getReuqestArchitectureContext' | 'getGenerateCodeContext' | 'resetStore'> = {
  pageType: PageType.app,
  sessionId: undefined,
  designComponent: {
    active: false,
    data: undefined,
    designComponentMap: {},
  },
  referImage: {
    active: false,
    reference: {
      structure: true,
      style: true,
      copywriting: false,
    },
  },
  nodeHtml: {
    active: false,
  },
};

const useAIStore = create<AIStore>()(
  immer((set, get): AIStore => {
    return {
      ...defaultStore,

      resetStore: () => {
        const state = get();
        set({ ...state, ...defaultStore });
      },

      getReuqestArchitectureContext: () => {
        const state = get();
        return {
          designComponent: {
            active: state.designComponent.active,
            data: filterDesignComponent(state.designComponent.data, state.designComponent.designComponentMap),
          },
          image: {
            active: state.referImage.active,
            data: state.referImage.data ?? '',
            reference: state.referImage.reference,
            customizeRequirement: state.referImage.customizeRequirement,
          },
        };
      },
      getGenerateCodeContext: async (pageArch: PageArchitecture) => {
        const state = get();
        return {
          designComponent: {
            active: state.designComponent.active,
            data: state.designComponent.active ? await getDesignComponentsResourceData(pageArch) : [],
          },
        };
      },
    };
  }),
);

if (import.meta.env.DEV) {
  mountStoreDevtool('AIStore', useAIStore);
}

export { useAIStore };
