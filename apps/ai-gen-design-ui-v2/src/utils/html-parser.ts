import { FigmaResult } from '@ai-assitant/ai-core';
import { HtmlParser, ImageHashInfo } from '@tencent/h2d-html-parser';

import { figmaMessages } from './figma';

const getHtmlParser = () =>
  new HtmlParser({
    apiUrl: import.meta.env.VITE_PLUGIN_SERVER_URL,
    source: 'figma',
    getImageHash: (args) => {
      return figmaMessages['get-image-hash'].request(args) as Promise<FigmaResult<ImageHashInfo>>;
    },
    getVideoHash: (args) => {
      return figmaMessages['get-video-hash'].request(args) as Promise<FigmaResult<string>>;
    },
    createDesign: (args) => {
      return figmaMessages['create-design'].request(args) as Promise<string>;
    },
    createDesignCollect: (isCreateAssets) => {
      figmaMessages['create-design-collect'].send({ isCreateAssets });
    },
  });

export default getHtmlParser;
