import { html as beautifyHtml } from 'js-beautify';

export interface FormatHtmlOptions {
  /** 缩进大小，默认为2 */
  indentSize?: number;
  /** 缩进字符，默认为空格 */
  indentChar?: ' ' | '\t';
  /** 最大保留换行数，默认为1 */
  maxPreserveNewlines?: number;
  /** 是否保留换行，默认为true */
  preserveNewlines?: boolean;
  /** 每行最大长度，默认为120 */
  wrapLineLength?: number;
  /** 是否在标签间添加换行，默认为true */
  wrapAttributes?:
    | 'auto'
    | 'force'
    | 'force-aligned'
    | 'force-expand-multiline'
    | 'aligned-multiple'
    | 'preserve'
    | 'preserve-aligned';
  /** 是否压缩空白字符，默认为false */
  collapseWhitespace?: boolean;
  /** 自闭合标签的处理方式 */
  voidElements?: boolean;
}

/**
 * 美化HTML代码
 * @param htmlCode - 需要美化的HTML代码
 * @param options - 格式化选项
 * @returns 美化后的HTML代码
 */
export function formatHtml(htmlCode: string, options: FormatHtmlOptions = {}): string {
  if (!htmlCode || typeof htmlCode !== 'string') {
    return htmlCode || '';
  }

  try {
    const defaultOptions = {
      indent_size: options.indentSize ?? 2,
      indent_char: options.indentChar ?? ' ',
      max_preserve_newlines: options.maxPreserveNewlines ?? 1,
      preserve_newlines: options.preserveNewlines ?? true,
      wrap_line_length: options.wrapLineLength ?? 120,
      wrap_attributes: options.wrapAttributes ?? 'auto',
      end_with_newline: true,
      indent_inner_html: true,
      indent_body_inner_html: true,
      indent_head_inner_html: true,
      indent_handlebars: true,
      unformatted: ['pre', 'code', 'textarea'],
      content_unformatted: ['pre', 'textarea'],
      extra_liners: ['head', 'body', '/html'],
    };

    return beautifyHtml(htmlCode.trim(), defaultOptions);
  } catch (error) {
    console.warn('HTML格式化失败:', error);
    // 如果美化失败，返回原始代码
    return htmlCode;
  }
}

/**
 * 压缩HTML代码（移除多余空白）
 * @param htmlCode - 需要压缩的HTML代码
 * @returns 压缩后的HTML代码
 */
export function minifyHtml(htmlCode: string): string {
  if (!htmlCode || typeof htmlCode !== 'string') {
    return htmlCode || '';
  }

  try {
    return beautifyHtml(htmlCode.trim(), {
      indent_size: 0,
      max_preserve_newlines: 0,
      preserve_newlines: false,
      wrap_line_length: 0,
      unformatted: ['pre', 'code', 'textarea'],
      content_unformatted: ['pre', 'textarea'],
    });
  } catch (error) {
    console.warn('HTML压缩失败:', error);
    return htmlCode;
  }
}
