import { MessageTypes } from '@ai-assitant/ai-core';

import { figmaMessages } from './figma';

export const DesignComponentsMaxLength = 500000;

import { DesignComponents, PageArchitecture } from '@/type';

export const getPickedComponentIds = (pageArchitecture: PageArchitecture) => {
  const pickedComponentSet = new Set<string>();
  pageArchitecture.pages?.forEach((page) => {
    page.sections?.forEach((section) => {
      section.dc?.forEach((id) => {
        pickedComponentSet.add(id);
      });
    });
  });
  return Array.from(pickedComponentSet);
};

export const getDesignComponents = async () => {
  const result = await figmaMessages[MessageTypes.AI_GEN_DESIGN].request({
    task: 'get-component-basic-data',
  });
  // const components = td;

  return result as { components: any[]; nodePageMap: Record<string, string> };
};

export const getDesignComponentsResourceData = async (pageArchitecture: PageArchitecture) => {
  const selectedComponents = getPickedComponentIds(pageArchitecture);
  const data = await figmaMessages[MessageTypes.AI_GEN_DESIGN].request({
    task: 'get-component-resource-data',
    nodeIds: selectedComponents,
  });
  return data;
};

export const designComponentsExceedLimit = (dc: any) => {
  return JSON.stringify(dc).length >= DesignComponentsMaxLength;
};

export const filterDesignComponent = (
  designComponents?: DesignComponents,
  designComponentMap?: Record<string, boolean>,
) => {
  if (!designComponents || !designComponentMap) {
    return [];
  }
  return designComponents?.filter((o) => designComponentMap?.[o.id]);
};
