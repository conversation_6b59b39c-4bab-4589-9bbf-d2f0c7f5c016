import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { stripIndents } from '@tencent/design-ai-utils';
import { useTranslation } from 'react-i18next';

import { Button, CopyButton } from '@/components';
import { useAIStore } from '@/store';
import { Page, Section } from '@/type';
import { cn } from '@/utils';

import SectionCard, { SectionCardProps } from './SectionCard';

interface PageCardProps {
  page?: Page;
  style?: React.CSSProperties;
  className?: string;
  onUpdatePage?: (id: string) => void;
  onUpdateSection?: (id: string) => void;
  onSectionClick?: (section: Section) => void;
  sectionDynamicProps?: (section: Section) => Partial<SectionCardProps>;
}

export default function PageCard({
  page,
  onUpdatePage,
  onUpdateSection,
  onSectionClick,
  style,
  className,
  sectionDynamicProps,
}: PageCardProps) {
  const pageSelectMap = useAIStore((s) => s.pageSelectMap);
  const { t } = useTranslation();
  const sectionIds = (page?.sections?.map((o) => o.id) ?? []) as string[];

  const { setNodeRef } = useDroppable({
    id: page?.id as string,
    data: {
      type: 'container',
      children: sectionIds,
    },
  });

  const formatCopyContent = (page: Page) => {
    return stripIndents`
      ${page.n}
      ${page.sections?.map((section) => `\n${section.n || ''}\n${section.d || ''}`)}
    `;
  };

  return (
    <div
      className={cn(
        'relative w-full rounded overflow-hidden h-max',
        {
          'border border-dashed border-white/50': !page,
        },
        className,
      )}
      style={style}
    >
      {/* page为空时 显示骨架屏 */}
      {!page ? (
        <>
          <div className="h-8 p-2 animate-pulse">
            <div className="w-2/3 h-4 bg-white/20 rounded"></div>
          </div>
          <div className="p-2 animate-pulse">
            <div className="h-4 mb-2 bg-white/20 rounded"></div>
            <div className="h-9 bg-white/20 rounded"></div>
          </div>
        </>
      ) : (
        <>
          <div className="h-8 flex items-center gap-2 pl-2 pr-1 text-xs bg-[#38441E] text-white">
            <input
              type="checkbox"
              checked={pageSelectMap[page.id!]}
              onChange={() => {
                useAIStore.setState((prev) => {
                  prev.pageSelectMap[page.id!] = !prev.pageSelectMap[page.id!];
                });
              }}
              className={cn(
                'w-4 h-4 relative rounded cursor-pointer appearance-none bg-white shrink-0',
                'after:content-[""] after:absolute after:left-[5px] after:top-[2px] after:w-[6px] after:h-[10px] after:border-r-2 after:border-b-2 after:border-black after:rotate-45 after:hidden',
                'checked:bg-primary checked:after:block',
              )}
            />
            <span className="flex-1 truncate">{page.n}</span>
            <div className="ml-auto flex items-center shrink-0">
              <Button border={false} onClick={() => page.id && onUpdatePage?.(page.id)}>
                {t('result-page.regenerate')}
              </Button>
              <CopyButton content={() => formatCopyContent(page)} />
            </div>
          </div>
          <div className="p-2 min-h-10 bg-white" ref={setNodeRef}>
            <SortableContext id={page.id} items={sectionIds} strategy={verticalListSortingStrategy}>
              {page.sections?.map((section) => {
                return (
                  <SectionCard
                    key={section.id}
                    section={section}
                    onUpdateSection={onUpdateSection}
                    onClick={onSectionClick}
                    {...(sectionDynamicProps?.(section) ?? {})}
                  />
                );
              })}
            </SortableContext>
          </div>
        </>
      )}
    </div>
  );
}
