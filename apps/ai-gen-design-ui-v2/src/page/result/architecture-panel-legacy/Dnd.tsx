import {
  CollisionDetection,
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  getFirstCollision,
  MouseSensor,
  pointerWithin,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { ReactNode, useEffect, useRef, useState } from 'react';

import { TRACK_ACTION } from '@/constant';
import { useAIStore } from '@/store';
import { Section } from '@/type';
import { track } from '@/utils';

import SectionCard from './SectionCard';

export default function Dnd(props: { children: ReactNode }) {
  const pageArchitecture = useAIStore((s) => s.pageArchitecture ?? {});
  const sensors = useSensors(useSensor(MouseSensor));

  const [dragingSection, setDragingSection] = useState<Section | undefined>();

  // 记录最后一次有效的碰撞结果
  const lastOverId = useRef<string | null>(null);
  const recentlyMovedToNewContainer = useRef(false);

  function findPageIndex(id: string) {
    return pageArchitecture.pages!.findIndex((p) => p.id === id);
  }

  function handleDragStart(event: DragStartEvent) {
    const { active } = event;
    if (!active) return;
    const activePageIndex = pageArchitecture.pages!.findIndex(
      (p) => p.id === active.data.current?.sortable.containerId,
    );
    if (activePageIndex === -1) return;
    setDragingSection(pageArchitecture.pages![activePageIndex].sections![active.data.current!.sortable.index]);
    track.send(TRACK_ACTION['drag-section']);
  }

  function handleDragOver(event: DragOverEvent) {
    const { active, over } = event;
    if (!over) return;
    const activeData = active.data.current ?? {};
    const overData = over.data.current ?? {};
    const activePageIndex = findPageIndex(activeData.sortable.containerId);
    const activeSecIndex = activeData.sortable.index;

    if (overData.type === 'container') {
      // 只有空容器才能接收元素
      useAIStore.setState((draft) => {
        const overPageIndex = findPageIndex(over.id as string);
        const pages = draft.pageArchitecture.pages!;
        const activeItems = pages[activePageIndex]!.sections!;
        const item = activeItems[activeSecIndex];
        pages[overPageIndex]!.sections = [item];
        activeItems.splice(activeSecIndex, 1);
      });
      return;
    }

    const overPageIndex = findPageIndex(overData.sortable.containerId);
    const overSecIndex = overData.sortable.index;
    if (activePageIndex === -1 || overPageIndex === -1) return;
    if (activePageIndex !== overPageIndex) {
      // 标记最近有跨容器移动
      recentlyMovedToNewContainer.current = true;

      useAIStore.setState((draft) => {
        const pages = draft.pageArchitecture.pages!;
        const activeItems = pages[activePageIndex]!.sections!;
        const overItems = pages[overPageIndex]!.sections!;
        const item = activeItems[activeSecIndex];
        activeItems.splice(activeSecIndex, 1);
        overItems.splice(overSecIndex, 0, item);
      });
    }
  }

  function handleDragEnd(event: DragEndEvent) {
    lastOverId.current = null;
    const { active, over } = event;

    if (!over) {
      setDragingSection(undefined);
      return;
    }

    const activePageIndex = findPageIndex(active.data.current?.sortable.containerId);
    const overPageIndex = findPageIndex(over.data.current?.sortable.containerId);

    if (activePageIndex === -1 || overPageIndex === -1) return;

    if (active.id !== over.id) {
      const activeSecIndex = active.data.current!.sortable.index;
      const overSecIndex = over.data.current!.sortable.index;
      useAIStore.setState((draft) => {
        const pages = draft.pageArchitecture.pages!;
        const activeItems = pages[activePageIndex]!.sections!;
        pages[activePageIndex]!.sections = arrayMove(activeItems, activeSecIndex, overSecIndex);
      });
    }
    setDragingSection(undefined);
  }

  const collisionDetectionStrategy: CollisionDetection = (args) => {
    // 先检查是否碰撞容器
    const _pointerOver = getFirstCollision(pointerWithin(args));
    const pointerOverData = _pointerOver?.data?.droppableContainer.data.current ?? {};

    if (_pointerOver && pointerOverData.type === 'container' && pointerOverData.children?.length === 0) {
      // 鼠标进入空容器，直接和该容器碰撞
      return [{ id: _pointerOver.id }];
    }

    const originDroppableRects = args.droppableRects;
    const newDroppableRects: typeof args.droppableRects = new Map();
    const newDroppableContainers: typeof args.droppableContainers = [];

    args.droppableContainers.forEach((o) => {
      if (o.data.current?.type !== 'container') {
        newDroppableRects.set(o.id, originDroppableRects.get(o.id)!);
        newDroppableContainers.push(o);
      }
    });
    const pointerIntersections = pointerWithin({
      ...args,
      droppableContainers: newDroppableContainers,
      droppableRects: newDroppableRects,
    });
    const pointerOver = getFirstCollision(pointerIntersections);

    let pointerOverId = pointerOver?.id;
    if (pointerOverId != null) {
      const overPage = pageArchitecture.pages?.find((page) =>
        page.sections?.some((section) => section.id === pointerOverId),
      );

      if (overPage) {
        const overPageSections = overPage.sections || [];

        if (overPageSections.length > 0) {
          // 过滤出同一页面（overPage）内的所有碰撞元素
          // 原因：鼠标可能同时覆盖多个页面的sections，会导致拖拽目标在不同页面间来回跳动
          const samePageIntersections = pointerIntersections.filter((intersection) => {
            const sectionId = intersection.id as string;
            return overPageSections.some((section) => section.id === sectionId);
          });

          if (samePageIntersections.length > 0) {
            pointerOverId = samePageIntersections[0].id;
            lastOverId.current = pointerOverId as string;
          }
        }
      }
      return [{ id: pointerOverId }];
    }

    if (recentlyMovedToNewContainer.current) {
      lastOverId.current = dragingSection?.id ?? null;
    }

    return lastOverId.current ? [{ id: lastOverId.current }] : [];
  };

  useEffect(() => {
    requestAnimationFrame(() => {
      recentlyMovedToNewContainer.current = false;
    });
  }, [pageArchitecture.pages]);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={collisionDetectionStrategy}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      {props.children}
      <DragOverlay style={{ zIndex: 1000 }}>
        {dragingSection ? <SectionCard section={dragingSection} className="bg-white" canInput={false} /> : null}
      </DragOverlay>
    </DndContext>
  );
}
