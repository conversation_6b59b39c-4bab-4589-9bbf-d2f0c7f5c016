import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTranslation } from 'react-i18next';
import { DragMoveIcon } from 'tdesign-icons-react';

import { Button, EditableDiv } from '@/components';
import { TRACK_ACTION } from '@/constant';
import { useAIStore } from '@/store';
import { Section } from '@/type';
import { cn, track } from '@/utils';

export interface SectionCardProps {
  section: Section;
  className?: string;
  style?: React.CSSProperties;
  canInput?: boolean;
  onUpdateSection?: (id: string) => void;
  onClick?: (section: Section) => void;
  showTryAgain?: boolean;
}

export default function SectionCard({
  section,
  className,
  style,
  canInput = false,
  onUpdateSection,
  onClick,
  showTryAgain = true,
}: SectionCardProps) {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, isDragging } = useSortable({
    id: section.id!,
  });
  const { t } = useTranslation();

  const handleSectionInput = (value: string, type: 'n' | 'd') => {
    useAIStore.setState((draft) => {
      const page = draft.pageArchitecture.pages?.find((page) => page.sections?.some((s) => s.id === section.id));
      if (!page) return;

      const findSection = page.sections?.find((s) => s.id === section.id);
      if (!findSection) return;

      findSection[type] = value;
    });

    track.send(TRACK_ACTION['input-update-section']);
  };

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      className={cn('relative box-border border border-black/10 rounded p-2 mb-2 group', className, {
        'border-primary': canInput,
        'opacity-50': isDragging,
      })}
      onClick={() => {
        onClick?.(section);
      }}
      style={{
        transform: CSS.Transform.toString(transform),
        transition: transform ? 'transform 200ms ease-in-out' : undefined,
        touchAction: 'none',
        ...style,
      }}
    >
      {/* section name */}
      <div className="flex flex-col items-center gap-1">
        <EditableDiv
          className={cn(
            'w-full text-black/50 rounded font-bold text-xs break-words outline-none',
            'border-2 border-transparent cursor-default focus:border-primary',
            canInput && 'hover:border-black/20 cursor-text',
          )}
          readonly={!canInput}
          value={section.n}
          onInput={(value) => handleSectionInput(value, 'n')}
        ></EditableDiv>

        {/* section desc */}
        <EditableDiv
          className={cn(
            'w-full text-black/40 text-xs break-words outline-none rounded',
            'border-2 border-transparent cursor-default focus:border-primary',
            canInput && 'hover:border-black/20 cursor-text',
          )}
          readonly={!canInput}
          value={section.d}
          onInput={(value) => handleSectionInput(value, 'd')}
        ></EditableDiv>
      </div>

      {/* 再试一次按钮 */}
      {showTryAgain && (
        <Button
          primary
          autoLoading
          border={false}
          className={cn(
            'w-max h-[22px] bg-gradient-to-r from-primary to-[#DAFF7B]',
            'absolute right-2 bottom-0 z-10 translate-y-1/2',
          )}
          onClick={() => section.id && onUpdateSection?.(section.id)}
        >
          {t('result-page.try-again')}
        </Button>
      )}

      {/* 移动手柄 */}
      <div
        ref={setActivatorNodeRef}
        className={cn(
          'absolute z-10 left-1/2 -translate-x-1/2 top-0 -translate-y-1/2',
          'invisible group-hover:visible',
          'h-4 w-6 bg-primary cursor-move rounded-md text-xs',
          'flex items-center justify-center',
        )}
        {...listeners}
      >
        <DragMoveIcon />
      </div>
    </div>
  );
}
