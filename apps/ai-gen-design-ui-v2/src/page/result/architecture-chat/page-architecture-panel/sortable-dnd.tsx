import {
  DndContext,
  Drag<PERSON>ndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToVerticalAxis, restrictToWindowEdges } from '@dnd-kit/modifiers';
import { PropsWithChildren, useState } from 'react';
import { createPortal } from 'react-dom';

import { Section } from '@/type';

import SectionCard from './section-card';

type PageArchitectureDndProps = PropsWithChildren<{
  onDragEnd?: (event: DragEndEvent) => void;
  onDragStart?: (event: DragStartEvent) => void;
}>;

export default function PageArchitectureDnd({ children, onDragEnd, onDragStart }: PageArchitectureDndProps) {
  const [dragingSection, setDragingSection] = useState<Section>();

  const sensors = useSensors(useSensor(MouseSensor));

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    if (!active) return;
    setDragingSection(active.data.current?.section);
    onDragStart?.(event);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    setDragingSection(undefined);
    onDragEnd?.(event);
  };

  return (
    <>
      <DndContext
        sensors={sensors}
        modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        {children}
        {createPortal(
          <DragOverlay>
            {dragingSection ? (
              <SectionCard showDragHandle readonly section={dragingSection} className="shadow-2xl" />
            ) : null}
          </DragOverlay>,
          document.body,
        )}
      </DndContext>
    </>
  );
}
