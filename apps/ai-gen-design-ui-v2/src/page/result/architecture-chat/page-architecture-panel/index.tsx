import { DragEndEvent } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { StreamJSONParser } from '@tencent/design-ai-utils';
import debounce from 'lodash/debounce';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { LoadingIcon } from 'tdesign-icons-react';
import { DialogPlugin } from 'tdesign-react';

import { aiUpdateArchitecture, manualUpdateArchitecture } from '@/api';
import { useCheckAlreadyGenerateSWR } from '@/api/swr';
import { Button } from '@/components';
import { IMessageItem4Render, Page, PageArchitecture, Section } from '@/type';
import { cn, simpleAlertDialog } from '@/utils';

import PageCard from './page-card';
import SortableDnd from './sortable-dnd';

export interface PageArchitecturePanelProps {
  messageItem: IMessageItem4Render;
  /** 是否有正在生成的任务 */
  getSomeoneGenerating?: () => boolean;
  /**
   * 生成代码
   * @param architectureMessageId
   * @param partialPageArchitecture 仅包含选中的页面
   */
  onGenerateCode?: (architectureMessageId: string, partialPageArchitecture: PageArchitecture) => void;
  onStopGenerateCode?: (architectureMessageId: string) => void;
  onGetCodeList?: (architectureMessageId: string) => void;
}
export interface PageArchitecturePanelRef {
  getArchitecture: () => PageArchitecture | undefined;
  getIsGenerating: () => boolean;
  getIsAlreadyGenerate: () => boolean;
}

const pageArchitectureIsEmpty = (architecture?: PageArchitecture) => {
  if (!architecture?.pages) return true;
  return architecture.pages.length === 0;
};

function initializePageSelectMap(pages?: Page[]) {
  return (
    pages?.reduce(
      (acc, page) => {
        acc[page.id!] = true;
        return acc;
      },
      {} as Record<string, boolean>,
    ) ?? {}
  );
}

function updateSectionInArchitecture(prev: PageArchitecture, newSection: Section) {
  if (!prev?.pages || !newSection) return prev;
  return {
    ...prev,
    pages: prev.pages.map((page) => {
      const hasTargetSection = page.sections?.some((s) => s?.id === newSection.id);
      if (!hasTargetSection) return page;

      return {
        ...page,
        sections: page.sections?.map((s) => (s.id === newSection.id ? newSection : s)) ?? [],
      };
    }),
  };
}

export default forwardRef<PageArchitecturePanelRef, PageArchitecturePanelProps>(function PageArchitecturePanel(
  { messageItem, getSomeoneGenerating, onGenerateCode, onStopGenerateCode, onGetCodeList },
  ref,
) {
  const [architecture, setArchitecture] = useState(() => messageItem.architecture);
  const [activePageId, setActivePageId] = useState<string>();
  const [pageSelectMap, setPageSelectMap] = useState<Record<string, boolean>>({});

  const [sectionRefreshing, setSectionRefreshing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const {
    data: isAlreadyGenerate,
    isLoading: isLoadingAlreadyGenerate,
    mutate: mutateAlreadyGenerate,
  } = useCheckAlreadyGenerateSWR(messageItem.metadata?.messageId);

  useImperativeHandle(
    ref,
    () => ({
      getArchitecture: () => architecture,
      getIsGenerating: () => isGenerating,
      getIsAlreadyGenerate: () => isAlreadyGenerate,
    }),
    [architecture, isGenerating, isAlreadyGenerate],
  );

  useEffect(() => {
    if (messageItem.architecture) {
      setArchitecture(messageItem.architecture);
      setPageSelectMap(initializePageSelectMap(messageItem.architecture?.pages));
      setActivePageId(messageItem.architecture?.pages?.at(messageItem.status === 'success' ? 0 : -1)?.id);
    }
  }, [messageItem.architecture, messageItem.status]);

  const pages = useMemo(() => architecture?.pages ?? [], [architecture?.pages]);
  const activePage = useMemo(() => pages.find((p) => p?.id === activePageId), [pages, activePageId]);
  const selectedPagesCount = useMemo(() => Object.values(pageSelectMap).filter(Boolean).length, [pageSelectMap]);

  const handleSaveArchitecture = useMemo(() => {
    return debounce(
      (arch: PageArchitecture) => {
        const { sessionId, messageId } = messageItem.metadata ?? {};
        if (!sessionId || !messageId) {
          return;
        }
        manualUpdateArchitecture({
          sessionId,
          messageId,
          pageArchitecture: arch,
        });
      },
      500,
      { leading: true, trailing: true },
    );
  }, [messageItem?.metadata]);

  if (pageArchitectureIsEmpty(architecture)) {
    return null;
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const activeIndex = activePage?.sections?.findIndex((section) => section.id === event.active.id);
    const overIndex = activePage?.sections?.findIndex((section) => section.id === event.over?.id);
    if (activeIndex !== undefined && overIndex !== undefined && activeIndex !== -1 && overIndex !== -1) {
      const newSections = arrayMove(activePage?.sections ?? [], activeIndex, overIndex);
      setArchitecture((prev) => {
        const newArchitecture = {
          ...prev,
          pages:
            prev?.pages?.map((page) => (page.id === activePageId ? { ...page, sections: newSections } : page)) ?? [],
        };
        handleSaveArchitecture(newArchitecture);
        return newArchitecture;
      });
    }
  };

  const handleSectionInput = (value: string, type: 'n' | 'd', section: Section) => {
    const activeIndex = activePage?.sections?.findIndex((s) => s.id === section.id);
    if (activeIndex !== undefined && activeIndex !== -1) {
      setArchitecture((prev) => {
        if (!prev) return prev;
        const newArchitecture = updateSectionInArchitecture(prev, {
          ...section,
          [type]: value,
        });
        handleSaveArchitecture(newArchitecture);
        return newArchitecture;
      });
    }
  };

  const handleSectionRefresh = (updatingSection: Section) => {
    const { sessionId, messageId } = messageItem?.metadata ?? {};
    if (!sessionId || !messageId || !architecture) {
      return;
    }
    return new Promise<void>((resolve) => {
      const jsonParser = new StreamJSONParser();
      setSectionRefreshing(true);
      aiUpdateArchitecture(
        {
          sessionId,
          messageId,
          pageArchitecture: architecture,
          updateMeasure: 'section',
          updateId: updatingSection.id,
          context: {},
        },
        {
          onMessage: (chunk) => {
            const chunkObj = JSON.parse(chunk);
            if (chunkObj?.type === '[TEXT]') {
              const newSection = jsonParser.feed(chunkObj.content);
              if (!newSection?.success) return;
              setArchitecture((prev) => {
                if (!prev?.pages) return prev;
                return updateSectionInArchitecture(prev, newSection.result);
              });
            }
          },
          onComplete: () => {
            setSectionRefreshing(false);
            resolve();
          },
        },
      ).catch(() => {
        setSectionRefreshing(false);
        resolve();
      });
    });
  };

  const handleGetCodeButtonClick = async () => {
    const architectureMessageId = messageItem.metadata?.messageId;
    if (!architectureMessageId) return;

    if (getSomeoneGenerating?.()) {
      simpleAlertDialog('当前有正在生成的任务');
      return;
    }

    await onGetCodeList?.(architectureMessageId);
  };

  const handleGenerateButtonClick = async () => {
    const architectureMessageId = messageItem.metadata?.messageId;
    if (!architectureMessageId) return;

    if (isGenerating) {
      const dialogInstance = DialogPlugin.confirm({
        body: '是否确定取消当前生成任务？',
        onConfirm: () => {
          setIsGenerating(false);
          dialogInstance.destroy();
          onStopGenerateCode?.(messageItem.metadata!.messageId!);
          mutateAlreadyGenerate();
        },
        onClose: () => {
          dialogInstance.destroy();
        },
      });
      dialogInstance.show();
      return;
    }

    if (getSomeoneGenerating?.()) {
      simpleAlertDialog('当前有正在生成的任务');
      return;
    }

    if (architecture) {
      try {
        setIsGenerating(true);
        await onGenerateCode?.(architectureMessageId, {
          ...architecture,
          pages: pages.filter((p) => pageSelectMap[p.id!]),
        });
      } finally {
        mutateAlreadyGenerate();
        setIsGenerating(false);
      }
    }
  };

  return (
    <div className="bg-gray-100 p-2 rounded-lg w-full min-w-[170px]">
      <div className="flex mb-2 gap-1 min-w-0 overflow-x-auto custom-scrollbar">
        {pages.map((page) => {
          return (
            <div
              key={page.id}
              onClick={() => setActivePageId(page.id)}
              className={cn(
                'w-max max-w-28 h-7 text-xs flex items-center gap-2 p-2 cursor-pointer rounded-lg hover:bg-black/20',
                activePageId === page.id && 'bg-black/20',
              )}
            >
              <input
                type="checkbox"
                onClick={(e) => e.stopPropagation()}
                checked={pageSelectMap[page.id!]}
                onChange={() => {
                  setPageSelectMap((prev) => ({
                    ...prev,
                    [page.id!]: !prev[page.id!],
                  }));
                }}
                className={cn(
                  'w-4 h-4 relative rounded cursor-pointer appearance-none bg-white shrink-0',
                  'after:content-[""] after:absolute after:left-[5px] after:top-[2px] after:w-[6px] after:h-[10px] after:border-r-2 after:border-b-2 after:border-black after:rotate-45 after:hidden',
                  'checked:bg-primary checked:after:block',
                )}
              />
              <span className="truncate">{page.n}</span>
            </div>
          );
        })}
      </div>

      <SortableDnd onDragEnd={handleDragEnd}>
        {activePage && (
          <PageCard
            page={activePage}
            sectionDynamicProps={() => ({
              readonly: messageItem.status !== 'success' || isAlreadyGenerate || sectionRefreshing || isGenerating,
              onInput: handleSectionInput,
              onRefreshClick: handleSectionRefresh,
            })}
          />
        )}
      </SortableDnd>
      {messageItem.status === 'success' &&
        (isAlreadyGenerate ? (
          <Button
            primary
            autoLoading
            className="mt-2 w-full"
            disabled={isLoadingAlreadyGenerate}
            onClick={handleGetCodeButtonClick}
          >
            查看代码
          </Button>
        ) : (
          <Button
            primary
            className="mt-2 w-full"
            disabled={selectedPagesCount === 0 || isLoadingAlreadyGenerate}
            onClick={handleGenerateButtonClick}
          >
            {isGenerating ? (
              <div className="flex items-center gap-2">
                <LoadingIcon />
                <span>正在生成</span>
              </div>
            ) : (
              `开始生成
            (${selectedPagesCount}/${pages.length})`
            )}
          </Button>
        ))}
    </div>
  );
});
