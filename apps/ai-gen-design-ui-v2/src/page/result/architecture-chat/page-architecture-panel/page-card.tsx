import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useEventListener, useUnmount } from 'ahooks';
import isNil from 'lodash/isNil';
import { useState } from 'react';

import { Page, Section } from '@/type';

import SectionCard, { SECTION_CARD_ID_PREFIX, SectionCardProps } from './section-card';

type SectionDynamicProps = Omit<SectionCardProps, 'section'>;

export default function PageCard({
  page,
  sectionDynamicProps,
}: {
  page: Page;
  sectionDynamicProps?: (section: Section) => SectionDynamicProps;
}) {
  const sectionIds = (page.sections?.map((section) => section.id)?.filter(Boolean) ?? []) as string[];
  const [activeSectionId, setActiveSectionId] = useState<string>();

  useUnmount(() => {
    setActiveSectionId(undefined);
  });

  useEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const sectionElement = target.closest(`[id^="${SECTION_CARD_ID_PREFIX}"]`);
    if (!sectionElement) {
      setActiveSectionId(undefined);
    }
  });

  const { setNodeRef } = useDroppable({
    id: page?.id as string,
    data: {
      type: 'page-container',
      children: sectionIds,
    },
  });

  return (
    <div className="flex flex-col gap-2" ref={setNodeRef}>
      <SortableContext id={page.id} items={sectionIds} strategy={verticalListSortingStrategy}>
        {page.sections?.map((section) => {
          const dynamicProps = sectionDynamicProps?.(section) ?? {};
          return (
            <SectionCard
              key={section.id}
              active={!isNil(activeSectionId) && activeSectionId === section.id}
              section={section}
              onClick={() => {
                setActiveSectionId(section.id);
                dynamicProps?.onClick?.(section);
              }}
              {...dynamicProps}
            />
          );
        })}
      </SortableContext>
    </div>
  );
}
