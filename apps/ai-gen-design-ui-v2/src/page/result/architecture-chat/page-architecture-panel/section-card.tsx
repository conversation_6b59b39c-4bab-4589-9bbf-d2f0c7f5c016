import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import React, { useState } from 'react';
import { DragMoveIcon } from 'tdesign-icons-react';

import { EditableDiv, SvgIcon } from '@/components';
import { Section } from '@/type';
import { cn } from '@/utils';

export const SECTION_CARD_ID_PREFIX = 'page-architecture-section';

export interface SectionCardProps {
  section: Section;
  className?: string;
  style?: React.CSSProperties;
  /** 只读时无法进行任何修改操作（拖拽、刷新、输入） */
  readonly?: boolean;
  /** 是否显示拖拽手柄，showDragHandle和readonly若同时true，则显示拖拽手柄但拖拽不生效 */
  showDragHandle?: boolean;
  /** 是否被选中（高亮状态） */
  active?: boolean;
  /** 点击section触发 */
  onClick?: (section: Section) => void;
  /** 点击刷新按钮触发 */
  onRefreshClick?: (section: Section) => void;
  /** 手动输入section的名称或描述触发 */
  onInput?: (value: string, type: 'n' | 'd', section: Section) => void;
}

export default function SectionCard({
  section,
  className,
  style,
  showDragHandle = false,
  readonly = true,
  active = false,
  onClick,
  onRefreshClick,
  onInput,
}: SectionCardProps) {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, isDragging } = useSortable({
    id: section.id!,
    data: {
      type: 'sortable-section',
      section,
    },
    disabled: readonly,
  });

  const [isPending, setIsPending] = useState(false);

  const handleSectionInput = (_value: string, _type: 'n' | 'd') => {
    onInput?.(_value, _type, section);
  };

  const handleSectionRefresh = async () => {
    setIsPending(true);
    try {
      await onRefreshClick?.(section);
    } finally {
      setIsPending(false);
    }
  };

  const sectionDescClassName = cn(
    'w-full rounded px-[6px] py-[1px] text-xs break-words outline-none',
    'border border-transparent transition-colors duration-200',
    !readonly && 'hover:bg-black/10 focus:bg-black/10 focus:cursor-text focus:caret-primary',
  );

  return (
    <div
      className={cn(
        'relative bg-white rounded p-2 pr-5 group text-xs border border-transparent cursor-default',
        isDragging && 'opacity-50',
        active && 'border-primary',
        className,
        isPending && 'loading-opacity',
      )}
      id={`${SECTION_CARD_ID_PREFIX}-${section.id}`}
      ref={setNodeRef}
      {...attributes}
      style={{
        transform: CSS.Transform.toString(transform),
        transition: transform && !isDragging ? 'transform 200ms ease-in-out' : undefined,
        touchAction: 'none',
        ...style,
      }}
      onClick={() => onClick?.(section)}
    >
      {/* section name */}
      <EditableDiv
        className={cn(sectionDescClassName, 'font-bold mb-[6px]')}
        readonly={readonly}
        value={section.n}
        onInput={(value) => handleSectionInput(value, 'n')}
      ></EditableDiv>
      {/* section description */}
      <EditableDiv
        className={sectionDescClassName}
        readonly={readonly}
        value={section.d}
        onInput={(value) => handleSectionInput(value, 'd')}
      ></EditableDiv>

      {/* refresh button */}
      {active && !readonly && (
        <div
          className="p-[3px] cursor-pointer bg-primary rounded-tl-sm rounded-br-sm absolute right-0 bottom-0 active:bg-primary/80"
          onClick={handleSectionRefresh}
        >
          <SvgIcon name="refresh" className="size-3" />
        </div>
      )}

      {/* 拖拽手柄 */}
      <div
        className={cn(
          'absolute left-1/2 -translate-x-1/2 -top-[8px] cursor-move z-20',
          readonly ? 'hidden' : 'hidden group-hover:block hover:block',
          showDragHandle && 'block',
        )}
        {...listeners}
        ref={setActivatorNodeRef}
      >
        <div className="size-3 px-[6px] py-[1px] box-content bg-primary rounded-sm flex items-center">
          <DragMoveIcon className="size-3" color="black" />
        </div>
      </div>
    </div>
  );
}
