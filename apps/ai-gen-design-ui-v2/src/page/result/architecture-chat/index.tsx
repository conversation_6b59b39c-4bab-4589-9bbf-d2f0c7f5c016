import { extractArchitecture } from '@tencent/design-ai-utils';
import { useMemoizedFn, useMount, useUnmount } from 'ahooks';
import { motion } from 'framer-motion';
import { useMemo, useRef } from 'react';
import { Loading } from 'tdesign-react';

import { requestTaskInfo, stopSession } from '@/api';
import { useHistoryMessagesSWR } from '@/api/swr';
import { EditableDiv, SendButton } from '@/components';
import { AI_GEN_DESIGN_URL_PREFIX } from '@/constant';
import { useChat, useInputValue, useSuggest } from '@/hooks';
import { MessageInfo } from '@/hooks/useXChat';
import { useAIStore } from '@/store';
import { IAgentMessage, IAgentMessageMetadata, IMessageItem4Render, PageArchitecture } from '@/type';
import { cn, simpleAlertDialog } from '@/utils';

import MessageListBox from '../message-list';
import Markdown from '../message-list/Markdown';
import PageArchitecturePanel, { PageArchitecturePanelProps, PageArchitecturePanelRef } from './page-architecture-panel';

// 使用 Vite HMR API 来跟踪模块是否已经初始化过
// if (import.meta.hot) {
//   // 初始化 HMR 数据对象
//   if (!import.meta.hot.data.hasInitialized) {
//     import.meta.hot.data.hasInitialized = false;
//   }

//   // 接受模块自身的热更新
//   import.meta.hot.accept((newModule) => {
//     if (newModule) {
//       console.log('[HMR] Module updated, preserving initialization state');
//     }
//   });
// }

const messagesConvert2Render = (list: MessageInfo<IAgentMessage>[]): IMessageItem4Render[] => {
  return list.map((item) => {
    const role = item.status === 'local' ? 'user' : 'ai';
    let architecture: PageArchitecture = {};
    let text = item.message.content;
    if (role === 'ai' && item.status !== 'error') {
      const extracted = extractArchitecture(item.message.content);
      text = extracted.text;
      architecture = extracted.architecture;
    }
    return {
      id: `${item.id}`,
      status: item.status,
      error: item.status === 'error' ? item.message.content : '',
      content: text,
      thinkContent: item.message.thinkContent,
      metadata: item.message.metadata,
      role,
      architecture,
    };
  });
};

export default function ArchitectureChat({
  initPrompt,
  architecturePanelDynamicProps,
}: {
  initPrompt?: string;
  architecturePanelDynamicProps?: (msg: IMessageItem4Render) => Partial<PageArchitecturePanelProps>;
}) {
  const archPanelRef = useRef<Record<string, PageArchitecturePanelRef>>({});
  const chatInputRef = useRef<HTMLDivElement>(null);
  const sessionId = useAIStore((s) => s.sessionId);
  const pageType = useAIStore((s) => s.pageType);

  const { inputValue, setInputValue, inputValueIsEmpty } = useInputValue('');

  const { messages, onRequest, setMessages, isRequesting, abortChatRequest } = useChat({
    stream: true,
    getFetchOptions: useMemoizedFn((isRegenerate, msg) => {
      if (isRegenerate && msg?.metadata?.messageId) {
        return {
          url: `${AI_GEN_DESIGN_URL_PREFIX}/architecture/regenerate`,
          options: {
            method: 'POST',
            body: {
              sessionId,
              pageType,
              messageId: msg.metadata.messageId,
              context: useAIStore.getState().getReuqestArchitectureContext(),
            },
          },
        };
      }
      return {
        url: `${AI_GEN_DESIGN_URL_PREFIX}/architecture/generate`,
        options: {
          method: 'POST',
          body: {
            sessionId,
            prompt: msg.content,
            pageType,
            context: useAIStore.getState().getReuqestArchitectureContext(),
          },
        },
      };
    }),
  });

  const stopRequest = () => {
    stopSession(sessionId!);
    abortChatRequest();
  };

  useUnmount(() => {
    stopRequest();
  });

  const unStoredMessages4Render = useMemo<IMessageItem4Render[]>(() => messagesConvert2Render(messages), [messages]);

  const { removeSuggestFormat, abortRequest: abortSuggestRequest } = useSuggest({
    inputRef: chatInputRef,
    onApply: (val) => {
      setInputValue(val);
    },
    referenceContent: inputValue,
    requestBody: useMemoizedFn(() => {
      const arch = getLatestArchitecture();
      return {
        pageType,
        update: arch ? 1 : 0,
        pageArchitecture: arch,
      };
    }),
  });

  const {
    historyMessages,
    requestHistoryMessages,
    isRequesting: isHistoryMessagesLoading,
    setHistoryMessages,
  } = useHistoryMessagesSWR(sessionId);

  const getTaskInfo = async () => {
    if (sessionId) {
      const res = await requestTaskInfo(sessionId);
      useAIStore.setState((draft) => {
        draft.pageType = res.pageType;
      });
      if (res.image?.url) {
        useAIStore.setState((draft) => {
          draft.referImage.url = res.image?.url;
          draft.referImage.data = res.image?.data;
          draft.referImage.active = true;
          draft.referImage.reference = res.image?.reference ?? {};
        });
      }
    }
  };

  useMount(() => {
    // 使用 Vite HMR API 检查是否是热更新后的重新挂载
    // if (import.meta.hot && import.meta.hot.data.hasInitialized) {
    //   console.log('[HMR] Skipping useMount side effects during hot reload');
    //   return;
    // }

    // 标记已经初始化过
    // if (import.meta.hot) {
    //   import.meta.hot.data.hasInitialized = true;
    // }

    if (initPrompt) {
      // 有初始prompt，则直接请求
      onRequest({ content: initPrompt }, false);
    } else if (sessionId) {
      // 获取历史消息
      requestHistoryMessages();
      getTaskInfo();
    }
  });

  const handleSend = (isRegenerate = false) => {
    if (inputValueIsEmpty) return;
    abortSuggestRequest();
    setInputValue('');
    onRequest({ content: inputValue }, isRegenerate);
  };

  const handleRegenerate = (metadata?: IAgentMessageMetadata) => {
    if (!metadata) return;
    const archPanel = archPanelRef.current[metadata.messageId];
    if (archPanel?.getIsGenerating()) {
      simpleAlertDialog('正在生成代码，不可以重新生成');
      return;
    }
    if (archPanel?.getIsAlreadyGenerate()) {
      simpleAlertDialog('已经生成过代码，不可以重新生成');
      return;
    }
    const msg = messages.find((item) => item.message?.metadata?.messageId === metadata.messageId);
    if (msg) {
      const cloneMessage: MessageInfo<IAgentMessage> = {
        ...msg,
        message: { metadata: msg.message.metadata, thinkContent: '', content: '' },
        status: 'loading',
      };
      setMessages(messages.map((item) => (item.id === msg.id ? cloneMessage : item)));
      onRequest(cloneMessage.message, true);
      return;
    }

    // 如果重新请求的是历史消息
    const historyMsg = historyMessages.find((item) => item.id === metadata.messageId);
    if (historyMsg) {
      setHistoryMessages(historyMessages.filter((item) => item.id !== historyMsg.id));
      setMessages([
        {
          id: historyMsg.id,
          status: 'loading',
          message: { metadata: historyMsg.metadata, thinkContent: '', content: '' },
        },
      ]);
      onRequest(historyMsg, true);
    }
  };

  const allMessages = useMemo(
    () => [...historyMessages, ...unStoredMessages4Render],
    [unStoredMessages4Render, historyMessages],
  );

  const getLatestArchitecture = useMemoizedFn(() => {
    const msg = allMessages.filter((msg) => msg.architecture)?.at(-1);
    if (msg?.metadata?.messageId) return archPanelRef.current[msg.metadata.messageId].getArchitecture();
  });

  return (
    <div className="flex flex-col h-full">
      <div className={cn('relative flex flex-col grow h-0 overflow-y-auto w-full')}>
        {isHistoryMessagesLoading && !isRequesting && (
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
            <Loading size="small" />
          </div>
        )}
        <MessageListBox
          className="flex-1 h-0 py-3"
          messageList={allMessages}
          onRegenerate={handleRegenerate}
          messageRender={(msg) => {
            if (msg.status === 'error' || msg.role !== 'ai') return false;
            return (
              <div className="flex flex-col gap-2 w-full">
                <Markdown markdownText={msg.content} />
                <PageArchitecturePanel
                  messageItem={msg}
                  ref={(ref) => {
                    if (!msg.metadata?.messageId) return;
                    if (!ref) delete archPanelRef.current[msg.metadata.messageId];
                    else archPanelRef.current[msg.metadata.messageId] = ref;
                  }}
                  {...architecturePanelDynamicProps?.(msg)}
                />
              </div>
            );
          }}
        />
      </div>

      <motion.div
        className="min-h-[84px] max-h-[160px] sticky flex flex-col border border-primaryBorder rounded bg-primaryBg p-3 pb-2"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 40 }}
        transition={{
          type: 'spring',
          stiffness: 200,
          damping: 12,
          mass: 0.8,
          bounce: 0.4,
        }}
      >
        <EditableDiv
          ref={chatInputRef}
          value={inputValue}
          className=" text-white caret-white flex-1 min-h-0"
          onInput={(val) => setInputValue(removeSuggestFormat(val))}
          onOnlyEnterDown={() => handleSend(false)}
        />
        <div className="h-9 flex items-center">
          <SendButton
            loading={isRequesting}
            className="ml-auto"
            onSend={() => handleSend(false)}
            disabled={inputValueIsEmpty}
            onStop={stopRequest}
          ></SendButton>
        </div>
      </motion.div>
    </div>
  );
}
