import { useEventListener, useLatest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';

import failedGif from '@/assets/failed.gif';
import loadingGif from '@/assets/loading.gif';
import successGif from '@/assets/success.gif';
import { PageCode, PageType } from '@/type';
import { cn } from '@/utils';

const AppWidth = 390;
const AppMaxHeight = 844;
const WebWidth = 1440;

function formatDesignComponentElement(code: string) {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(code, 'text/html');

    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      return code;
    }

    const presetClass = 'bg-gray-100';
    const dcElements = doc.querySelectorAll('[dc="1"]');

    dcElements.forEach((element) => {
      element.innerHTML = '';
      if (element.hasAttribute('class')) {
        const classValue = element.getAttribute('class') || '';
        if (!classValue.includes(presetClass)) {
          element.setAttribute('class', `${classValue} ${presetClass}`.replace(/\s+/g, ' ').trim());
        }
      } else {
        element.setAttribute('class', presetClass);
      }
    });

    const serializer = new XMLSerializer();
    return serializer.serializeToString(doc);
  } catch (error) {
    // 如果有任何错误，直接返回原始代码
    console.error('Error processing HTML:', error);
    return code;
  }
}

function addMobileSpecificCode(code: string) {
  const viewportMeta = `<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">`;
  code = code.replace('<html>', `<html><head>${viewportMeta}</head>`);
  const hideScrollbarStyles = `
        <style>
          ::-webkit-scrollbar {
            display: none !important;
          }
          body {
            scrollbar-width: none !important;
            -ms-overflow-style: none !important;
            margin: 0;
            padding: 0;
          }
        </style>
      `;
  code = code.replace('</head>', `${hideScrollbarStyles}</head>`);
  return code;
}

export default function PreviewFrame({ pageCode, pageType }: { pageCode: PageCode; pageType: PageType }) {
  const [tempUrl, setTempUrl] = useState<string>('');
  const [isFrameLoading, setIsFrameLoading] = useState(true);
  const [iframeWrapperStyle, setIframeWrapperStyle] = useState<React.CSSProperties>({});
  const iframeWrapperRef = useRef<HTMLDivElement>(null);

  const isMobile = useLatest(pageType === 'app');
  const notReady = pageCode.success == null;
  const isFailed = pageCode.success === false;

  const computeIframeWrapperStyle = () => {
    if (!iframeWrapperRef.current) return;
    const wrapperWidth = iframeWrapperRef.current.clientWidth;
    const wrapperHeight = iframeWrapperRef.current.clientHeight;

    if (isMobile.current) {
      const scale = 0.8;
      const height = (wrapperHeight / scale) * 0.95;
      setIframeWrapperStyle({
        width: AppWidth,
        height,
        maxHeight: AppMaxHeight,
        transform: `scale(${scale})`,
        transformOrigin: 'center',
        position: 'absolute',
        inset: '0',
        margin: 'auto',
      });
    } else {
      const scale = wrapperWidth / WebWidth;
      setIframeWrapperStyle({
        width: WebWidth,
        height: wrapperHeight / scale,
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
      });
    }
  };

  useEventListener('resize', computeIframeWrapperStyle, { target: window });

  useEffect(() => {
    computeIframeWrapperStyle();
    setIsFrameLoading(true);
    let code = pageCode.code ?? '';

    if (isMobile.current) {
      code = addMobileSpecificCode(code);
    }
    code = formatDesignComponentElement(code);

    const blob = new Blob([code], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    setTempUrl(url);

    return () => {
      URL.revokeObjectURL(url);
    };
  }, [pageCode]);

  return (
    <div ref={iframeWrapperRef} className="relative size-full overflow-hidden">
      {isFailed && (
        <div className="absolute inset-0 w-full h-full flex items-center justify-center bg-gray-50 z-10">
          <div className="flex flex-col items-center gap-2 p-2">
            <img src={failedGif} className="size-40" />
            <span className="text-red-500 text-xs">{pageCode.error}</span>
            {/* <Button primary>{t('retry')}</Button> */}
          </div>
        </div>
      )}

      {(isFrameLoading || notReady) && (
        <div className="absolute inset-0 w-full h-full flex items-center justify-center bg-gray-50 z-10">
          <img src={notReady ? loadingGif : successGif} className="size-48" />
        </div>
      )}

      <div className="relative bg-white" style={iframeWrapperStyle}>
        {!isMobile.current && (
          <div className="w-full h-[40px] bg-gray-800 text-white text-xs px-4 flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 rounded-full bg-red-500"></div>
              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
              <div className="w-2 h-2 rounded-full bg-green-500"></div>
            </div>
            <div className="flex-1 bg-gray-700 rounded-full px-3 py-1 text-center truncate text-gray-300">
              example.com
            </div>
          </div>
        )}
        <iframe
          src={tempUrl}
          className={cn('w-full min-h-0 h-full', {
            'h-[calc(100%-40px)]': !isMobile.current,
          })}
          sandbox="allow-scripts"
          onLoad={() => setIsFrameLoading(false)}
          style={{
            border: 'none',
            overflow: 'hidden',
          }}
        />
      </div>
    </div>
  );
}
