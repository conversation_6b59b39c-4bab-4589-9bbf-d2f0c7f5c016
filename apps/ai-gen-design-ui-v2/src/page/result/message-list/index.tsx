import { Bubble } from '@ant-design/x';
import { BubbleDataType } from '@ant-design/x/es/bubble/BubbleList';
import { useDebounce } from 'ahooks';
import React, { memo, useEffect, useMemo, useRef } from 'react';

import aiAvatar from '@/assets/ai-avatar.svg';
import { IAgentMessageMetadata, IMessageItem4Render } from '@/type';
import { cn } from '@/utils';

import Message from './Message';
import ChatMessageFooter from './MessageFooter';

export interface MessageListBoxProps extends React.HTMLAttributes<HTMLDivElement> {
  messageList: IMessageItem4Render[];
  onRegenerate?: (metadata?: IAgentMessageMetadata) => void;
  /**
   * 自定义消息渲染，返回false则使用内置的渲染组件
   */
  messageRender?: (messageItem: IMessageItem4Render) => React.ReactNode;
}

const MessageListBox = ({ messageList, className, onRegenerate, messageRender }: MessageListBoxProps) => {
  // 监听 items 更新，滚动到最底部
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const items = useMemo<BubbleDataType[]>(
    () =>
      messageList.map((item, index) => {
        const isLast = index === messageList.length - 1;
        return {
          key: `${item.id}-${item.role}`,
          prefixCls: 'assistant-bubble',
          content: item.content,
          role: item.role,
          loading: item.status === 'loading' && !item.content,
          messageRender: () => {
            const internalRender = () => <Message item={item} />;
            if (!messageRender) return internalRender();
            const customRenderElement = messageRender(item);
            return customRenderElement !== false ? customRenderElement : internalRender();
          },
          footer: item.role === 'ai' && item.status === 'success' && (
            <div className="flex items-center">
              <ChatMessageFooter
                message={item}
                onRegenerate={() => {
                  onRegenerate?.(item.metadata);
                }}
                showRegenerateButton={isLast}
              />
            </div>
          ),
        };
      }),
    [messageList],
  );

  const debounceItems = useDebounce(items, { wait: 50 });

  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        behavior: 'smooth',
        top: scrollContainerRef.current.scrollHeight,
      });
    }
  }, [debounceItems]);

  return (
    <div className={cn('w-full h-full overflow-y-auto custom-scrollbar pr-1', className)} ref={scrollContainerRef}>
      <Bubble.List
        items={items}
        roles={{
          ai: {
            placement: 'start',
            classNames: {
              content: '!bg-transparent !p-0 !min-h-[36px] !leading-5 !w-[80%]',
            },
            avatar: <img src={aiAvatar} className="size-6 shrink-0" />,
          },
          user: {
            placement: 'end',
            classNames: {
              content: '!bg-transparent !p-0 !min-h-[36px] !leading-5 !max-w-[80%]',
            },
          },
        }}
        className="w-full mx-auto md:px-0 box-border overflow-hidden"
      />
    </div>
  );
};

export default memo(MessageListBox);
