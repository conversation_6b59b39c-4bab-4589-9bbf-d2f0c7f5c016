import { stripIndents } from '@tencent/design-ai-utils';
import * as clipboard from 'clipboard-polyfill';
import { useState } from 'react';
import { MessagePlugin, Tooltip } from 'tdesign-react';

import { useMessageFeedbackSWR } from '@/api/swr';
import { Button } from '@/components';
import { MESSAGE_PLUGIN_CONFIG } from '@/constant';
import { IMessageItem4Render, IRating } from '@/type';

interface IChatMessageFooterProps {
  message: IMessageItem4Render;
  onRegenerate?: (messageId: string) => void;
  showRegenerateButton?: boolean;
}

/**
 * 消息底部操作区
 */
export default function ChatMessageFooter(props: IChatMessageFooterProps) {
  const { message, onRegenerate, showRegenerateButton } = props;

  const [isLiked, setIsLiked] = useState(message.feedback === 'like');
  const [isDisLiked, setIsDisLiked] = useState(message.feedback === 'dislike');

  const { trigger } = useMessageFeedbackSWR(message.id);

  const handleCopy = () => {
    let copyContent = message.content;
    if (message.architecture) {
      copyContent += message.architecture.pages?.map((page) => {
        return stripIndents`
        ${page.n}
        ${page.sections?.map((section) => `\n${section.n || ''}\n${section.d || ''}`)}
        `;
      });
    }
    clipboard.writeText(copyContent);
    MessagePlugin.success({ content: '复制成功', ...MESSAGE_PLUGIN_CONFIG });
  };

  const handleRating = (type: IRating) => {
    if (type === 'like') {
      setIsDisLiked(false);
      setIsLiked((prev) => !prev);
      trigger(isLiked ? null : 'like');
    } else if (type === 'dislike') {
      setIsLiked(false);
      setIsDisLiked((prev) => !prev);
      trigger(isDisLiked ? null : 'dislike');
    }
  };

  /**
   * 操作按钮列表
   */
  const actionButtons = [
    {
      key: 'message-regenerate',
      icon: 'refresh',
      hidden: !showRegenerateButton,
      onClick: () => {
        onRegenerate?.(message.id);
      },
    },
    {
      key: 'message-copy',
      icon: 'copy',
      onClick: handleCopy,
      active: false,
      hidden: false,
    },
    {
      key: 'message-like',
      icon: 'thumbs-up',
      onClick: () => {
        handleRating('like');
      },
      active: isLiked,
      hidden: false,
    },
    {
      key: 'message-dislike',
      icon: 'thumbs-down',
      onClick: () => {
        handleRating('dislike');
      },
      active: isDisLiked,
      hidden: false,
    },
  ];

  return (
    <div className="flex items-center gap-2">
      {actionButtons.map(
        ({ key, icon, onClick, active, hidden }) =>
          !hidden && (
            <Tooltip delay={300} content={key} placement="top">
              <Button
                className={`bg-transparent border-none size-4 p-[1px] hover:bg-transparent ${active ? 'text-primary' : 'text-white/40'}`}
                onClick={onClick}
                icon={icon}
              ></Button>
            </Tooltip>
          ),
      )}
    </div>
  );
}
