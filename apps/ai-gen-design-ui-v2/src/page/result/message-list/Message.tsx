import { memo } from 'react';
import { ErrorCircleIcon } from 'tdesign-icons-react';

import { IMessageItem4Render } from '@/type';

import Markdown from './Markdown';

interface IMessageContentProps {
  /**
   * 消息数据对象
   */
  item: IMessageItem4Render;
}

const ChatMessage = (props: IMessageContentProps) => {
  const { item } = props;
  const { status, error, content, role } = item;

  // 如果是错误状态，则直接展示错误信息
  if (status === 'error') {
    return (
      <p className="flex items-center h-full py-2 px-3 bg-red-100 rounded-lg border border-red-500 text-red-500">
        <ErrorCircleIcon className="mr-2" />
        {error}
      </p>
    );
  }

  // 本地消息或用户消息
  if (role === 'local' || role === 'user') {
    return (
      <div className="flex items-center h-full py-2 px-3 rounded-tl-lg rounded-br-lg rounded-tr-sm rounded-bl-lg bg-primary/70">
        <Markdown markdownText={content} />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2 h-full rounded-lg">
      <Markdown markdownText={content} />
    </div>
  );
};

export default memo(ChatMessage);
