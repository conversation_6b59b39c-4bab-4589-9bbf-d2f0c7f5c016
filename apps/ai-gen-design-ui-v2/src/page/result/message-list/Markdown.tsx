import flow from 'lodash/flow';
import { memo } from 'react';
import ReactMarkdown from 'react-markdown';
import RehypeRaw from 'rehype-raw';
import RehypeSanitize, { defaultSchema } from 'rehype-sanitize';
import RemarkBreaks from 'remark-breaks';
import RemarkGfm from 'remark-gfm';

interface IMarkdownRendererProps {
  /**
   * 原始 Markdown 文本
   */
  markdownText: string;
}

const preprocessThinkTag = (content: string) => {
  return flow([
    (str: string) => str.replace('<think>\n', '<details think=true>\n'),
    (str: string) => str.replace('\n</think>', '\n[ENDTHINKFLAG]</details>'),
  ])(content);
};

const sanitizeSchema = {
  ...defaultSchema,
  attributes: {
    ...defaultSchema.attributes,
    details: [['think']],
  },
};

/**
 * Markdown 渲染组件
 */
const MarkdownRenderer = ({ markdownText }: IMarkdownRendererProps) => {
  const latexContent = flow([preprocessThinkTag])(markdownText);

  return (
    <div className={'w-full text-xs overflow-hidden flex flex-col gap-1 text-white'}>
      <ReactMarkdown
        remarkPlugins={[RemarkGfm, RemarkBreaks]}
        rehypePlugins={[RehypeRaw, [RehypeSanitize, sanitizeSchema]]}
        disallowedElements={['iframe', 'head', 'html', 'meta', 'link', 'style', 'body']}
      >
        {latexContent}
      </ReactMarkdown>
    </div>
  );
};

export default memo(MarkdownRenderer);
