import { useMemoizedFn, useUnmount } from 'ahooks';
import { ReactNode, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, PageLayout } from '@/components';
import { APP_WIDTH, TRACK_ACTION, WEB_WIDTH } from '@/constant';
import { useGenerateCode } from '@/hooks';
import { useAIStore } from '@/store';
import { PageCode } from '@/type';
import { getHtmlParser, track } from '@/utils';

import ArchitectureChat from './architecture-chat';
import CodePanel from './code-panel';
import GenerateProgress from './GenerateProgress';

export default function ResultPage({
  initPrompt,
  navigateToMainPage,
}: {
  initPrompt?: string;
  navigateToMainPage: () => void;
}) {
  const { t } = useTranslation();

  const [activeBreadcrumb, setActiveBreadcrumb] = useState<'architecture' | 'code'>('architecture');
  const currentArchitectureMessageId = useRef<string | null>(null);
  const {
    pageCodes,
    currentPageCode,
    isGenerating,
    runGenerateTask,
    stopGenerateTask,
    setCurrentPageCodeIndex,
    requestCodeList,
  } = useGenerateCode();

  useUnmount(() => {
    if (currentArchitectureMessageId.current) stopGenerateTask(currentArchitectureMessageId.current);
  });

  const handleInsertCanvas = async (pages: PageCode[]) => {
    const { pageType } = useAIStore.getState();
    for (const p of pages) {
      if (p.success && p.code) {
        try {
          await getHtmlParser().create({
            html: p.code,
            name: `${p?.pageName} (id: ${p?.codeId})`,
            pageParams: {
              viewport: pageType === 'app' ? APP_WIDTH : WEB_WIDTH,
              theme: 'light',
              isUseAutoLayout: false,
            },
          });
        } catch (error: any) {
          track.send(TRACK_ACTION['insert-canvas-error'], {
            eventValue: {
              error: error.message,
            },
          });
        }
      }
    }
  };

  const renderFooter = (tipElement: ReactNode) => (
    <div className="w-full flex justify-between items-center">
      {isGenerating ? (
        <GenerateProgress
          totalNum={pageCodes.length}
          finishedNum={pageCodes.filter((p) => p.completed).length}
          className="max-w-[460px]"
        />
      ) : activeBreadcrumb === 'architecture' ? (
        tipElement
      ) : (
        <div className="text-white/60 text-xs flex gap-1">
          {t('total')} <span className="text-primary">{pageCodes.length}</span> {t('item')}，{t('success')}
          <span className="text-primary">{pageCodes.filter((p) => p.success).length}</span> {t('item')}，{t('failed')}
          <span className="text-red-500">{pageCodes.filter((p) => !p.success).length}</span> {t('item')}
        </div>
      )}
      <div className="flex gap-2">
        {activeBreadcrumb === 'code' && (
          <>
            <Button
              primary
              autoLoading
              border={false}
              disabled={!currentPageCode?.success || currentPageCode?.code?.length === 0}
              className="w-36 shrink-0"
              icon="star"
              onClick={() => {
                if (currentPageCode) {
                  track.send(TRACK_ACTION['insert-canvas'], {
                    eventValue: { type: 'single' },
                  });
                  return handleInsertCanvas([currentPageCode]);
                }
              }}
            >
              {t('insert-canvas')}
            </Button>
            <Button
              autoLoading
              border
              disabled={pageCodes.some((p) => p.success == null)}
              className="w-36 shrink-0 text-primary border-primary"
              icon="star"
              onClick={() => {
                track.send(TRACK_ACTION['insert-canvas'], {
                  eventValue: { type: 'all' },
                });
                return handleInsertCanvas(pageCodes);
              }}
            >
              {t('all-insert-canvas')}
            </Button>
          </>
        )}
      </div>
    </div>
  );

  const breadcrumb = [
    {
      label: t('result-page.architecture'),
      key: 'architecture',
      active: activeBreadcrumb === 'architecture',
      onClick: () => {
        if (activeBreadcrumb !== 'architecture') {
          setActiveBreadcrumb('architecture');
        }
      },
    },
    {
      label: t('result-page.result'),
      key: 'result',
      active: activeBreadcrumb === 'code',
      disabled: pageCodes.length === 0,
      onClick: () => {
        if (activeBreadcrumb !== 'code') {
          setActiveBreadcrumb('code');
        }
      },
    },
  ];

  const getSomeoneGenerating = useMemoizedFn(() => isGenerating);

  return (
    <PageLayout
      breadcrumb={breadcrumb}
      onBack={navigateToMainPage}
      bodyClassName="flex flex-col overflow-y-hidden"
      footerRender={renderFooter}
    >
      <div className="h-full" style={{ display: activeBreadcrumb === 'architecture' ? 'block' : 'none' }}>
        <ArchitectureChat
          initPrompt={initPrompt}
          architecturePanelDynamicProps={() => {
            return {
              onGenerateCode: async (...args) => {
                setActiveBreadcrumb('code');
                await runGenerateTask(...args);
              },
              onStopGenerateCode: stopGenerateTask,
              onGetCodeList: async (architectureMessageId) => {
                await requestCodeList(architectureMessageId);
                setActiveBreadcrumb('code');
              },
              getSomeoneGenerating,
            };
          }}
        />
      </div>

      {activeBreadcrumb === 'code' && (
        <CodePanel
          pageCodes={pageCodes}
          currentPageCode={currentPageCode}
          changeCurrentPageCode={setCurrentPageCodeIndex}
        />
      )}
    </PageLayout>
  );
}
