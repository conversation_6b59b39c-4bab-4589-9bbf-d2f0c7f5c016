import { useLatest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';

import { cn } from '@/utils';

export default function GenerateProgress({
  totalNum = 5,
  finishedNum = 0,
  className,
}: {
  totalNum?: number;
  finishedNum?: number;
  className?: string;
}) {
  const [progress, setProgress] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const formatProgress = (Math.min(1, progress) * 100).toFixed(0);
  const progressRef = useLatest(progress);
  const maxProgressPercent = useRef(100);

  useEffect(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    if (progressRef.current < 1) {
      if (finishedNum >= totalNum) {
        setProgress(1);
      } else {
        maxProgressPercent.current = 100 - totalNum * 5 + finishedNum * 5;
        const maxProgress = Math.min(1, Math.max(0, maxProgressPercent.current / 100));

        const incrementProgress = () => {
          setProgress((prev) => {
            const increment = Math.random() * 0.03;
            const nextProgress = Math.min(prev + increment, maxProgress);

            if (nextProgress < maxProgress) {
              timerRef.current = setTimeout(incrementProgress, 500 + Math.random() * 1000);
            }

            return nextProgress;
          });
        };
        timerRef.current = setTimeout(incrementProgress, 1000 + Math.random() * 1000);
      }
    }

    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [totalNum, finishedNum, progressRef]);

  return (
    <div className={cn('h-4 w-full flex items-center gap-2', className)}>
      <span className="size-[6px] bg-primary rounded-full shrink-0"></span>
      <div className="relative flex-1 min-w-0 bg-[#E7ECF6]/60 rounded h-1 overflow-hidden">
        <div
          className="absolute left-0 top-0 h-full rounded bg-primary bg-gradient-to-r from-primary to-[#DAFF7B] transition-all duration-500"
          style={{ width: `${formatProgress}%` }}
        ></div>
      </div>
      <div className="w-10 text-primary text-xs font-bold shrink-0 flex items-center">{formatProgress} %</div>
    </div>
  );
}
