import { MessageTypes } from '@ai-assitant/ai-core';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircleFilledIcon, LoadingIcon } from 'tdesign-icons-react';

import { useAIStore } from '@/store';
import { figmaMessages, filterDesignComponent } from '@/utils';

export default function DesignComponentPanel({ onClick, loading }: { onClick?: () => void; loading?: boolean }) {
  const { t } = useTranslation();
  const { active, data, designComponentMap } = useAIStore((s) => s.designComponent);
  const [fileInfo, setFileInfo] = useState<{ name: string; cover: string | null } | null>(null);

  useEffect(() => {
    if (active) {
      figmaMessages[MessageTypes.AI_GEN_DESIGN]
        .request({
          task: 'get-file-info',
        })
        .then((fileInfo: any) => {
          setFileInfo(fileInfo);
        });
    } else {
      setFileInfo(null);
    }
  }, [active]);

  const componentCount = useMemo(() => {
    return filterDesignComponent(data, designComponentMap).length;
  }, [designComponentMap, data]);

  return (
    <div
      onClick={onClick}
      className="flex gap-3 h-12 mb-3 cursor-pointer items-center border border-primaryBorder rounded bg-primaryBg overflow-hidden px-2"
    >
      {loading ? (
        <LoadingIcon className="text-2xl text-primary" />
      ) : (
        <CheckCircleFilledIcon className="text-2xl text-primary" />
      )}
      <div className="flex flex-col gap-1 justify-end">
        <div className="text-xs text-white">{fileInfo?.name || ''}</div>
        <div className="text-xs text-white/60">{t('component-count', { count: componentCount ?? 0 })}</div>
      </div>
    </div>
  );
}
