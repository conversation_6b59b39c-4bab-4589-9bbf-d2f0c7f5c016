import { useMemo } from 'react';

import { Button } from '@/components';
import { useAIStore } from '@/store';

export default function NodeHtmlPanel({ onClick }: { onClick: () => void }) {
  const { data } = useAIStore((s) => s.nodeHtml);

  const count = useMemo(() => {
    let _count = 0;
    data?.forEach((o) => {
      _count += o.html.length;
    });
    return _count;
  }, [data]);

  return (
    <div
      onClick={onClick}
      className="relative flex gap-4 h-max mb-3 items-center border border-primaryBorder rounded bg-primaryBg overflow-hidden p-2 text-sm text-white/50 cursor-pointer"
    >
      通过选中节点生成<span className="text-primary font-bold">{count}</span>个html模板代码
      <Button
        border={false}
        icon="close"
        onClick={() =>
          useAIStore.setState((draft) => {
            draft.nodeHtml.active = false;
            draft.nodeHtml.data = undefined;
          })
        }
        className="absolute top-2 right-2 hover:bg-white/10"
      ></Button>
    </div>
  );
}
