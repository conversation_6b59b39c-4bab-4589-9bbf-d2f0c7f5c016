import { selectionEmitter } from '@ai-assitant/ai-figma';
import { produce } from 'immer';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DialogPlugin, MessagePlugin, Switch, Textarea, Tooltip } from 'tdesign-react';
import { useShallow } from 'zustand/react/shallow';

import { createTask } from '@/api';
import { SessionItem } from '@/api/swr';
import logo from '@/assets/logo.png';
import logoFoot from '@/assets/logo-foot.png';
import { Button, EditableDiv, PageLayout, SvgIcon, Tabs, TagGroup } from '@/components';
import { DesignStyleKeywords, MESSAGE_PLUGIN_CONFIG, PageTypes, TRACK_ACTION } from '@/constant';
import { useDetect, useInputValue, useSuggest } from '@/hooks';
import { Locales } from '@/locale';
import { useAIStore } from '@/store';
import type { DesignStyleKeyword, PageType } from '@/type';
import {
  convertFileToBase64,
  cosClient,
  createFileElementUpload,
  designComponentsExceedLimit,
  filterDesignComponent,
  getDesignComponents,
  track,
} from '@/utils';

import DesignComponentPanel from './DesignComponentPanel';
import DesignComponentSelector from './DesignComponentSelector';
import HistoryDrawer from './HistoryDrawer';
import NodeHtmlPanel from './NodeHtmlPanel';
import NodeHtmlPreview from './NodeHtmlPreview';
import PromptExample from './PromptExample';
import ReferImagePanel from './ReferImagePanel';

export default function MainPage({ navigateToResultPage }: { navigateToResultPage: (initPrompt?: string) => void }) {
  const { t, i18n } = useTranslation();
  const { inputValue, setInputValue, inputValueIsEmpty } = useInputValue('');
  const { inputValue: designStylePrompt, setInputValue: setDesignStylePrompt } = useInputValue('');
  const [designStyleKeywords, setDesignStyleKeywords] = useState<DesignStyleKeyword[]>([]);
  const pageType = useAIStore((s) => s.pageType);
  const [showHistory, setShowHistory] = useState(false);
  const { designComponent, referImage, nodeHtml } = useAIStore(
    useShallow((s) => ({
      designComponent: s.designComponent,
      referImage: s.referImage,
      nodeHtml: s.nodeHtml,
    })),
  );
  const chatInputRef = useRef<HTMLDivElement>(null);

  // 获取设计组件的loading
  const [getDesignComponentLoading, setGetDesignComponentLoading] = useState(false);
  // 是否显示组件选择面板
  const [dcSelectorOpen, setDcSelectorOpen] = useState(false);
  const [nodeHtmlPreviewOpen, setNodeHtmlPreviewOpen] = useState(false);
  const [figmaHasSelection, setFigmaHasSelection] = useState(false);

  useEffect(() => {
    selectionEmitter.on((selection) => {
      setFigmaHasSelection(selection.length > 0);
    });

    return () => {
      selectionEmitter.off();
    };
  }, []);

  const { isLoading, setIsLoading, getHtml } = useDetect();

  const { removeSuggestFormat } = useSuggest({
    inputRef: chatInputRef,
    onApply: (val) => setInputValue(val),
    referenceContent: inputValue,
    requestBody: { pageType },
  });

  const handleSwitchDesignComponentMode = async () => {
    const newDesignComponentMode = !designComponent.active;
    useAIStore.setState((draft) => {
      draft.designComponent.active = newDesignComponentMode;
    });

    if (newDesignComponentMode) {
      setGetDesignComponentLoading(true);

      try {
        const data = await getDesignComponents();
        MessagePlugin.success({ content: '获取组件数据成功', ...MESSAGE_PLUGIN_CONFIG });
        useAIStore.setState((draft) => {
          draft.designComponent.data = data.components;
          draft.designComponent.designComponentMap = data.components?.reduce(
            (acc, o) => {
              acc[o.id] = true;
              return acc;
            },
            {} as Record<string, boolean>,
          );
        });
        (window as any).$nodePageMap = data.nodePageMap;
        if (designComponentsExceedLimit(data)) {
          setDcSelectorOpen(true);
        }
      } catch {
        MessagePlugin.error({ content: '获取组件数据失败', ...MESSAGE_PLUGIN_CONFIG });
        useAIStore.setState((draft) => {
          draft.designComponent.active = false;
          draft.designComponent.designComponentMap = {};
        });
      } finally {
        setGetDesignComponentLoading(false);
      }
    } else {
      setGetDesignComponentLoading(false);
    }
  };

  const handleUploadImage = () => {
    track.send(TRACK_ACTION['upload-reference-image']);

    const destory = createFileElementUpload(
      async (files) => {
        const base64 = await convertFileToBase64(files[0]);
        if (!base64) return;

        useAIStore.setState((draft) => {
          if (!draft.referImage.reference) {
            draft.referImage.reference = { structure: true, style: true, copywriting: false };
          }
          draft.referImage.previewBase64 = base64;
          draft.referImage.active = true;
          draft.referImage.uploading = true;
        });

        try {
          const uploadResult = await cosClient.uploadFile({
            file: files[0],
            params: {
              onFileFinish: (err) => {
                if (err) {
                  console.error('上传失败:', err);
                }
              },
            },
          });
          useAIStore.setState((draft) => {
            draft.referImage.active = true;
            draft.referImage.previewBase64 = undefined;
            draft.referImage.data = uploadResult.fileKey;
            draft.referImage.url = uploadResult.url;
            draft.referImage.uploading = false;
          });

          MessagePlugin.success({ content: '图片上传成功', ...MESSAGE_PLUGIN_CONFIG });
        } catch {
          MessagePlugin.error({ content: '上传失败', ...MESSAGE_PLUGIN_CONFIG });

          useAIStore.setState((draft) => {
            draft.referImage.active = false;
            draft.referImage.previewBase64 = undefined;
            draft.referImage.data = undefined;
            draft.referImage.uploading = false;
          });
        }

        destory();
      },
      {
        accept: 'image/jpeg,image/jpg,image/png',
        limitExceededMessage: t('refer-image.limit-exceeded', { size: 5 }),
      },
    );
  };

  const handleDetectHtml = async () => {
    setIsLoading(true);
    const html = await getHtml();
    console.log('html', html);
    if (!html) {
      MessagePlugin.error({ content: '识别失败', ...MESSAGE_PLUGIN_CONFIG });
    } else {
      const map = new Map();
      html.forEach((node: any) => {
        node.componentNodes?.forEach((o: any) => {
          const minifyHtml = o.html?.replace(/\s+/g, ' ').replace(/\n+/g, '\n');
          if (minifyHtml) {
            const codeList = [...(map.get(o.class) ?? [])];
            codeList.push(minifyHtml);
            map.set(o.class, codeList);
          }
        });
      });
      if (map.size) {
        useAIStore.setState((draft) => {
          draft.nodeHtml.active = true;
          draft.nodeHtml.data = Array.from(map.entries()).map(([key, value]) => ({ name: key, html: value }));
        });
      } else {
        MessagePlugin.warning({ content: '未识别到有效代码', ...MESSAGE_PLUGIN_CONFIG });
      }
    }
    setIsLoading(false);
  };

  // 选择的组件
  const filteredDesignComponents = useMemo(
    () => filterDesignComponent(designComponent.data, designComponent.designComponentMap),
    [designComponent.data, designComponent.designComponentMap],
  );
  // 组件内容量是否超出限制
  const exceedLimit = useMemo(() => {
    return designComponent.active && designComponentsExceedLimit(filteredDesignComponents);
  }, [designComponent.active, filteredDesignComponents]);

  const goNextStep = async () => {
    if (exceedLimit) {
      setDcSelectorOpen(true);
    } else {
      const res = await createTask(pageType);
      if (!res?.taskSessionId) return;
      useAIStore.setState({ sessionId: res.taskSessionId });
      let prompt = inputValue;
      if (designStylePrompt) {
        prompt += `\n设计风格要求：${designStylePrompt}`;
      }
      if (designStyleKeywords.length) {
        prompt += `\n设计风格关键词：${designStyleKeywords.map((k) => k.value[i18n.language as Locales]).join(',')}`;
      }
      navigateToResultPage(prompt);
    }
  };

  const handleCustomizeReferImageRequirement = () => {
    const dialogInstance = DialogPlugin({
      header: '自定义垫图要求',
      body: <CustomizeReferImageRequirementTextarea />,
      footer: false,
      onClose: () => {
        dialogInstance.destroy();
      },
    });

    dialogInstance.show();
  };

  const handleSelectHistory = (item: SessionItem) => {
    useAIStore.setState({ sessionId: item.id });
    setShowHistory(false);
    navigateToResultPage();
  };

  return (
    <PageLayout
      title={t('main-page.title')}
      headerButtons={[
        <Button icon="historical-conversation" className="border-none" onClick={() => setShowHistory(true)}></Button>,
      ]}
      footer={
        <Button
          primary
          autoLoading
          border={false}
          disabled={inputValueIsEmpty || getDesignComponentLoading}
          className="w-48"
          icon="star"
          onClick={goNextStep}
        >
          {t('main-page.next-step')}
        </Button>
      }
    >
      <div className="text-xs text-white/50 mb-2">{t('main-page.device-select')}</div>
      <Tabs
        className="mb-2 shrink-0"
        value={pageType}
        items={PageTypes.map((o) => ({
          ...o,
          label: o.label[i18n.language as Locales],
        }))}
        onChange={(v) => useAIStore.setState({ pageType: v as PageType })}
      />

      <div className="relative">
        <img src={logo} className="absolute w-16 h-14 right-3 -top-10 z-0" />
        <img src={logoFoot} className="absolute w-16 h-4 right-5 -top-2 z-20" />
        <div className="relative z-10 border border-primaryBorder rounded bg-primaryBg p-3 pb-2">
          {designComponent.active && (
            <DesignComponentPanel onClick={() => setDcSelectorOpen(true)} loading={getDesignComponentLoading} />
          )}
          {referImage.active && <ReferImagePanel />}
          {nodeHtml.active && <NodeHtmlPanel onClick={() => setNodeHtmlPreviewOpen(true)} />}
          {/* <div className="text-xs text-white mb-2">
            {t('main-page.press')}
            <span className="border border-white/40 rounded px-2 mx-1">Tab</span> {t('main-page.suggest-tip')}
          </div> */}
          <EditableDiv
            ref={chatInputRef}
            value={inputValue}
            className="h-[92px] text-white caret-white"
            onInput={(val) => setInputValue(removeSuggestFormat(val))}
            placeholder={t('main-page.input-placeholder')}
          />

          <div className="my-3 border-white/20 border-t border-dashed" />

          <div>
            <EditableDiv
              value={designStylePrompt}
              className="h-[40px] text-white caret-white"
              onInput={(val) => setDesignStylePrompt(val)}
              placeholder={t('main-page.style-placeholder')}
            />
            <div className="mt-2 text-xs text-white flex items-center">
              <span className="text-white/60 mr-2">{t('reference')}: </span>
              <TagGroup
                items={DesignStyleKeywords.map((keyword) => ({
                  children: keyword.value[i18n.language as Locales],
                  className: 'text-white/60',
                  onClick: () => {
                    setDesignStyleKeywords((prev) => {
                      return produce(prev, (draft) => {
                        const index = draft.findIndex((k) => k.key === keyword.key);
                        if (index !== -1) {
                          draft.splice(index, 1);
                        } else {
                          draft.push(keyword);
                        }
                      });
                    });
                  },
                  highlight: designStyleKeywords.some((k) => k.key === keyword.key),
                }))}
              ></TagGroup>
            </div>
          </div>

          <div className="flex gap-4 h-9 items-center mt-3">
            <div className="flex items-center gap-2 text-xs text-white/60">
              {t('main-page.design-component')}
              :
              <Switch
                value={designComponent.active}
                onChange={handleSwitchDesignComponentMode}
                className="checked:bg-primary"
              />
            </div>
            {/* <Button className="px-3 py-1" disabled={!figmaHasSelection} onClick={handleDetectHtml} loading={isLoading}>
              基于选中节点生成
            </Button> */}
            {/* <Button onClick={handleCustomizeReferImageRequirement}>自定义垫图要求</Button> */}

            <Tooltip content={t('refer-image.tip')} theme="light" showArrow={false}>
              <SvgIcon
                name="add-image"
                className="w-5 h-5 mr-2 text-white cursor-pointer ml-auto"
                onClick={handleUploadImage}
              />
            </Tooltip>
          </div>
        </div>
      </div>

      <div className="shrink-0 mt-6 mb-5">
        <PromptExample
          pageType={pageType}
          onSelect={(v) => {
            setInputValue(v);
            chatInputRef.current?.focus();
          }}
        />
      </div>

      <DesignComponentSelector
        open={dcSelectorOpen}
        onClose={() => setDcSelectorOpen(false)}
        exceedLimit={exceedLimit}
      />
      <NodeHtmlPreview open={nodeHtmlPreviewOpen} onClose={() => setNodeHtmlPreviewOpen(false)} />

      <HistoryDrawer open={showHistory} onClose={() => setShowHistory(false)} onSelect={handleSelectHistory} />
    </PageLayout>
  );
}

function CustomizeReferImageRequirementTextarea() {
  const referImage = useAIStore((s) => s.referImage);

  return (
    <Textarea
      value={referImage.customizeRequirement}
      onChange={(val) => {
        useAIStore.setState((draft) => {
          draft.referImage.customizeRequirement = val;
        });
      }}
    />
  );
}
