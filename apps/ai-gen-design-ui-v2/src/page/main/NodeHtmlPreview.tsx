import { useState } from 'react';

import { Drawer, Tabs } from '@/components';
import { useAIStore } from '@/store';

export default function NodeHtmlPreview({ open, onClose }: { open: boolean; onClose: () => void }) {
  const { data } = useAIStore((s) => s.nodeHtml);
  const [tabsValue, setTabsValue] = useState(data?.[0]?.name);

  return (
    <Drawer open={open} onClose={onClose} placement="bottom">
      <div className="flex flex-col h-full">
        <Tabs
          className="mb-2 shrink-0"
          value={tabsValue}
          items={
            data?.map((o) => ({
              label: o.name,
              value: o.name,
            })) ?? []
          }
          onChange={(v) => setTabsValue(v)}
        />

        <div className="flex-1 overflow-y-auto border border-primaryBorder rounded p-2 flex flex-col gap-3">
          {data
            ?.filter((o) => o.name === tabsValue)
            .map((o) => o.html?.map((h) => <div key={h} dangerouslySetInnerHTML={{ __html: h }}></div>))}
        </div>
      </div>
    </Drawer>
  );
}
