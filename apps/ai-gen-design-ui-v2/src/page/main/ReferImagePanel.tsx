import { motion } from 'framer-motion';
import { useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { LoadingIcon } from 'tdesign-icons-react';
import { Loading } from 'tdesign-react';

import { Button, TagGroup } from '@/components';
import { useAIStore } from '@/store';
import { ReferImageContent } from '@/type';

export default function ReferImagePanel() {
  const { active, data, url, reference, uploading, previewBase64 } = useAIStore((s) => s.referImage);
  const [preview, setPreview] = useState(false);
  const { t } = useTranslation();

  const referContentItems = useMemo(() => {
    if (!active) return [];
    return (['structure', 'style', 'copywriting'] as ReferImageContent[]).map((o) => ({
      children: t(`refer-image.${o}`),
      onClick: () => {
        useAIStore.setState((draft) => {
          if (!draft.referImage.active) return;
          draft.referImage.reference[o] = !draft.referImage.reference[o];
        });
      },
      highlight: reference?.[o],
    }));
  }, [t, active, reference]);

  return (
    <>
      <div className="relative flex gap-4 h-max mb-3 items-center border border-primaryBorder rounded bg-primaryBg overflow-hidden p-2 flex-col min-[380px]:flex-row">
        <Loading size="small" indicator={<LoadingIcon className="text-primary" />} loading={uploading}>
          <img
            src={url ?? previewBase64}
            className="h-24 max-w-52 cursor-pointer shrink-0 min-w-0"
            onClick={() => setPreview(true)}
          />
        </Loading>
        <div className="flex flex-col gap-1 h-24 justify-between">
          <div className="text-xs text-white/50">{t('refer-image.tip')}</div>
          <div className="text-xs text-white">{t('refer-image.select-content')}</div>
          <TagGroup items={referContentItems} wrapperClassName="mt-auto" />
        </div>

        <Button
          border={false}
          icon="close"
          onClick={() =>
            useAIStore.setState((draft) => {
              draft.referImage.active = false;
              draft.referImage.data = undefined;
              draft.referImage.previewBase64 = undefined;
              draft.referImage.uploading = false;
            })
          }
          className="absolute top-2 right-2 hover:bg-white/10"
        ></Button>
      </div>
      {preview &&
        createPortal(
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="fixed inset-0 z-50 bg-black/50 m-auto size-screen flex items-center justify-center"
            onClick={() => setPreview(false)}
          >
            <img src={data ?? previewBase64} className="size-[90%] object-contain" />
          </motion.div>,
          document.body,
        )}
    </>
  );
}
