import { useDebounce, useDebounceFn } from 'ahooks';
import { formatDistance } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { MoreIcon, SearchIcon } from 'tdesign-icons-react';
import { Dropdown, Input } from 'tdesign-react';

import { deleteSession } from '@/api';
import { SessionItem, useSessionListSWR } from '@/api/swr';
import { Drawer } from '@/components';
import i18n, { Locales } from '@/locale';

const PAGE_SIZE = 20;

const timeAgo = (time: string) => {
  return formatDistance(new Date(time), Date.now(), {
    addSuffix: true,
    locale:
      i18n.language === Locales.EN_US
        ? {
            ...enUS,
            formatDistance: (token, count, options) => {
              if (token === 'lessThanXMinutes' && count === 1) {
                return 'Just now';
              }

              return enUS.formatDistance(token, count, options);
            },
          }
        : {
            ...zhCN,
            formatDistance: (token, count, options) => {
              if (token === 'lessThanXMinutes' && count === 1) {
                return '刚刚';
              }

              return zhCN.formatDistance(token, count, options);
            },
          },
  });
};

export default function HistoryDrawer({
  open,
  onClose,
  onSelect,
}: {
  open: boolean;
  onClose: () => void;
  onSelect: (item: SessionItem) => void;
}) {
  const scrollViewportRef = useRef<HTMLDivElement>(null);

  /**
   * 搜索值
   */
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchValue = useDebounce(searchValue, { wait: 300 });
  /** 是否存在重命名过的会话 */
  const hasRenamed = useRef(false);

  const {
    data = [],
    isValidating,
    size,
    setSize,
    mutate,
  } = useSessionListSWR({ pageSize: PAGE_SIZE, title: debouncedSearchValue });

  /** 重新验证所有page的数据 */
  const revalidateAllPageData = useCallback(async () => {
    const freshData = await mutate();
    if (!freshData || freshData.length === 0) {
      setSize(1);
      return;
    }

    // list有数据的page才被认为是有效page
    const validPageCount = freshData.reduce((acc, curr) => acc + (curr?.list?.length > 0 ? 1 : 0), 0);
    if (validPageCount !== size) {
      setSize(validPageCount);
    }
  }, [mutate, size, setSize]);

  useEffect(() => {
    if (open) {
      revalidateAllPageData();
    } else if (hasRenamed.current) {
      // 关闭时，如果存在重命名过的会话，则重新验证数据
      hasRenamed.current = false;
      revalidateAllPageData();
    }
  }, [open]);

  const sessionList = useMemo(() => data.reduce((acc, curr) => acc.concat(curr.list), [] as any[]), [data]);
  /** 没有数据 */
  const isEmpty = sessionList.length === 0;
  /** 正在加载更多数据 */
  const isLoadingMore = isValidating && data && data.length < size;
  /** 到达底部 */
  const isReachingEnd = isEmpty || (data && data.at(-1)?.hasMore === false);

  const { run: handleScroll } = useDebounceFn(
    () => {
      if (isLoadingMore || !scrollViewportRef.current || isReachingEnd) return;
      const { scrollTop, clientHeight, scrollHeight } = scrollViewportRef.current;
      if (scrollHeight - (scrollTop + clientHeight) < 50) {
        setSize(size + 1);
      }
    },
    { wait: 300 },
  );

  return (
    <Drawer open={open} onClose={onClose} placement="left">
      <div className="flex flex-col items-center gap-5 h-full min-h-0 py-5">
        {sessionList.length === 0 ? (
          <div className="flex pt-2 justify-center h-full text-white/60">暂无历史记录</div>
        ) : (
          <>
            <Input
              align="left"
              className="shrink-0"
              inputClass="border-none !outline-none !shadow-none bg-primaryBorder caret-primary h-8 !text-white/60"
              autofocus
              placeholder=""
              suffixIcon={<SearchIcon className="!text-white" />}
              type="text"
              value={searchValue}
              onChange={setSearchValue}
            />
            <div
              className="flex-1 min-h-0 w-full overflow-y-auto custom-scrollbar"
              onScroll={handleScroll}
              ref={scrollViewportRef}
            >
              {sessionList.map((item) => {
                return (
                  <div className="flex h-16 w-full p-3 rounded-lg cursor-pointer gap-2 group hover:bg-black/20 text-white mb-4">
                    <div className="flex-1 min-w-0 h-full" onClick={() => onSelect(item)}>
                      <div className="flex items-center justify-between h-1/2">
                        <div className="flex-1 truncate font-medium text-xs">{item.title}</div>
                        <div className="w-max shrink-0 text-xs text-white/60">{timeAgo(item.updated_at)}</div>
                      </div>
                      <div className="w-full leading-5 truncate text-white/60 text-xs mt-1 h-1/2">{item.sub_title}</div>
                    </div>

                    <Dropdown
                      options={[
                        // {
                        //   content: '重命名',
                        //   divider: true,
                        //   onClick: () => {
                        //     console.log('重命名');
                        //   },
                        // },
                        {
                          content: '删除',
                          theme: 'error',
                          onClick: () => {
                            deleteSession(item.id).then(() => {
                              mutate();
                            });
                          },
                        },
                      ]}
                      placement="bottom-left"
                      trigger="click"
                    >
                      <div className="shrink-0 w-4 rounded hover:bg-primaryBg items-center justify-center hidden group-hover:flex">
                        <MoreIcon className="w-4 h-4" />
                      </div>
                    </Dropdown>
                  </div>
                );
              })}
              {isLoadingMore && (
                <div className="flex items-center justify-center h-10 text-white/60 text-xs">加载中...</div>
              )}
            </div>
          </>
        )}
      </div>
    </Drawer>
  );
}
