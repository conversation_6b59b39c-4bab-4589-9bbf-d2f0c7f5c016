import 'tdesign-react/esm/style/index.js';
import '@/style/global.less';

import { useCallback, useState } from 'react';

import { ResizeWindow } from '@/components';
import { useAIStore } from '@/store';
import { RoutePath } from '@/type';

import MainPage from './main';
import ResultPage from './result';

export default function PageRoot() {
  const [currentRoute, setCurrentRoute] = useState<RoutePath>('main');
  const [initPrompt, setInitPrompt] = useState<string | undefined>();

  const navigateToMainPage = useCallback(() => {
    setCurrentRoute('main');
    setInitPrompt('');
    useAIStore.getState().resetStore();
  }, []);

  const navigateToResultPage = useCallback((initPrompt?: string) => {
    setCurrentRoute('result');
    setInitPrompt(initPrompt);
  }, []);

  return (
    <>
      {currentRoute === 'main' ? (
        <MainPage navigateToResultPage={navigateToResultPage} />
      ) : (
        <ResultPage initPrompt={initPrompt} navigateToMainPage={navigateToMainPage} />
      )}
      <ResizeWindow />
    </>
  );
}
