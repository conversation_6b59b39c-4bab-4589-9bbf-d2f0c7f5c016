import { motion, useAnimation } from 'framer-motion';
import { useEffect } from 'react';

import { useMergedState } from '@/hooks';
import { cn } from '@/utils';

import Button from '../button';

interface DrawerProps {
  open?: boolean;
  onClose?: () => void;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
  placement?: 'left' | 'right' | 'bottom';
  title?: string;
  headerButtons?: React.ReactNode[];
  className?: string;
}

export default function Drawer({
  open,
  onClose,
  onOpenChange,
  children,
  placement = 'bottom',
  title,
  headerButtons,
  className,
}: DrawerProps) {
  const controls = useAnimation();
  const wrapperControls = useAnimation();

  const [isOpen, setIsOpen] = useMergedState(false, {
    value: open,
    onValueChange: (val) => {
      if (!val) onClose?.();
      onOpenChange?.(val);
    },
  });

  const getAnimationProps = () => {
    switch (placement) {
      case 'left':
        return {
          initial: { opacity: 0, x: -100 },
          open: { opacity: 1, x: 0 },
          close: { opacity: 0, x: -100 },
        };
      case 'right':
        return {
          initial: { opacity: 0, x: 100 },
          open: { opacity: 1, x: 0 },
          close: { opacity: 0, x: 100 },
        };
      case 'bottom':
      default:
        return {
          initial: { opacity: 0, y: 100 },
          open: { opacity: 1, y: 0 },
          close: { opacity: 0, y: 100 },
        };
    }
  };

  const getContainerStyles = () => {
    switch (placement) {
      case 'left':
        return cn(
          'absolute left-0 top-0 h-full w-[80%] bg-primaryBg rounded-r-lg',
          'after:content-[""] after:absolute after:left-[-100px] after:top-0 after:w-[100px] after:h-screen after:bg-primaryBg',
        );
      case 'right':
        return cn(
          'absolute right-0 top-0 h-full w-[80%] bg-primaryBg rounded-l-lg',
          'after:content-[""] after:absolute after:right-[-100px] after:top-0 after:w-[100px] after:h-screen after:bg-primaryBg',
        );
      case 'bottom':
      default:
        return cn(
          'absolute left-0 bottom-0 w-full h-[80%] bg-primaryBg rounded-t-lg',
          'after:content-[""] after:absolute after:bottom-[-100px] after:w-full after:h-[100px] after:bg-primaryBg',
        );
    }
  };

  const animationProps = getAnimationProps();

  useEffect(() => {
    if (isOpen) {
      controls.start(animationProps.open);
      wrapperControls.start({ opacity: 1 });
    } else {
      controls.start(animationProps.close);
      wrapperControls.start({ opacity: 0 });
    }
  }, [isOpen, controls, wrapperControls, animationProps, placement]);

  const handleClose = async () => {
    await Promise.all([controls.start(animationProps.close), wrapperControls.start({ opacity: 0 })]);
    setIsOpen(false);
  };

  const onWrapperClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return (
    <motion.div
      className={cn('fixed inset-0 z-50 size-screen bg-black/60', !isOpen && 'hidden')}
      onClick={onWrapperClick}
      initial={{ opacity: 0 }}
      animate={wrapperControls}
    >
      <motion.div
        className={getContainerStyles()}
        initial={animationProps.initial}
        animate={controls}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 30,
        }}
      >
        {isOpen && (
          <div className={cn('p-4 size-full', className)}>
            <div className="flex items-center mb-2 gap-3 w-full">
              <h1 className="text-white text-lg font-bold">{title}</h1>
              <div className="ml-auto flex items-center gap-2">
                {headerButtons}
                <Button icon="close" onClick={handleClose} border={false}></Button>
              </div>
            </div>
            {children}
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
