import { useTranslation } from 'react-i18next';

import { Locales } from '@/locale';

import Button from '../button';

/**
 * 语言切换组件
 */
export default function LanguageButton(props: { className?: string }) {
  const { i18n } = useTranslation();

  const handleSwitch = () => {
    i18n.changeLanguage(i18n.language === Locales.ZH_CN ? Locales.EN_US : Locales.ZH_CN);
  };

  return (
    <Button
      border={false}
      icon={i18n.language === Locales.ZH_CN ? 'to-en' : 'to-zh'}
      onClick={handleSwitch}
      className={props.className}
    >
    </Button>
  );
}
