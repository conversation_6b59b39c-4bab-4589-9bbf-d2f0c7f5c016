import { forwardRef, KeyboardEvent, useEffect, useRef } from 'react';

import { cn } from '@/utils';

import { convertInnerHtmlToText, moveCaretToEnd } from './helper';

interface EditableDivProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onInput'> {
  value?: string;
  placeholder?: string;
  readonly?: boolean;
  onInput?: (value: string) => void;
  onOnlyEnterDown?: (e: KeyboardEvent<HTMLDivElement>) => void;
}

export default forwardRef<HTMLDivElement, EditableDivProps>(function EditableDiv(props, ref) {
  const {
    value,
    className,
    placeholder = '请输入...',
    readonly = false,
    onInput,
    onCompositionStart,
    onCompositionEnd,
    onFocus,
    onBlur,
    onOnlyEnterDown,
    ...restProps
  } = props;

  const inputRef = useRef<HTMLDivElement>();
  const isUserInput = useRef(false);
  const composing = useRef(false);
  const focus = useRef(false);

  useEffect(() => {
    // 只有在非用户输入（即外部 value 变化）时才进行 innerHTML 的同步更新
    if (!isUserInput.current) {
      updateInnerHTML(value ?? '');
    }
    isUserInput.current = false;
  }, [value]);

  const updateInnerHTML = (val: string) => {
    const convertedValue = val.replace(/\n/g, '<br />');
    if (inputRef.current && inputRef.current.innerHTML !== convertedValue) {
      inputRef.current.innerHTML = convertedValue;
      // 将光标移动到最后
      if (focus.current) {
        moveCaretToEnd(inputRef.current);
      }
    }
  };

  const handleInput = (innerHTMLString: string) => {
    if (composing.current) return;

    isUserInput.current = true;
    let val = convertInnerHtmlToText(innerHTMLString);

    // 只有一个单换行符时，代表此时没有内容，清空innerHTML
    if (val === '\n') {
      val = '';
      inputRef.current!.innerHTML = '';
    }

    onInput?.(val);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey && !e.metaKey && !composing.current) {
      e.preventDefault();
      onOnlyEnterDown?.(e);
    } else {
      props.onKeyDown?.(e);
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLDivElement>) => {
    e.preventDefault();

    const text = e.clipboardData.getData('text/plain');
    if (!text) return;

    // 获取当前选区
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // 删除当前选中的内容
    selection.deleteFromDocument();

    const textWithBreaks = text.replace(/\n/g, '<br />');
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = textWithBreaks;

    const range = selection.getRangeAt(0);
    const fragment = document.createDocumentFragment();
    while (tempDiv.firstChild) {
      fragment.appendChild(tempDiv.firstChild);
    }
    range.insertNode(fragment);

    // 将光标移动到插入内容的末尾
    range.collapse(false);
    selection.removeAllRanges();
    selection.addRange(range);

    handleInput(inputRef.current?.innerHTML || '');
  };

  return (
    <div
      ref={(el) => {
        inputRef.current = el!;
        if (typeof ref === 'function') {
          ref(el);
        } else if (ref) {
          ref.current = el;
        }
      }}
      contentEditable={!readonly}
      onInput={(e) => handleInput(e.currentTarget.innerHTML)}
      onPaste={handlePaste}
      className={cn(
        'outline-none text-xs break-words overflow-auto',
        'empty:before:content-[attr(data-placeholder)] empty:before:text-white/50 empty:before:pointer-events-none',
        '[&::-webkit-scrollbar]:w-1',
        '[&::-webkit-scrollbar-thumb]:bg-white/20',
        '[&::-webkit-scrollbar-thumb]:rounded-full',
        '[&::-webkit-scrollbar-track]:bg-transparent',
        className,
      )}
      data-placeholder={placeholder}
      onCompositionStart={(e) => {
        composing.current = true;
        onCompositionStart?.(e);
      }}
      onCompositionEnd={(e) => {
        composing.current = false;
        handleInput(e.currentTarget.innerHTML);
        onCompositionEnd?.(e);
      }}
      onFocus={(e) => {
        focus.current = true;
        onFocus?.(e);
      }}
      onBlur={(e) => {
        focus.current = false;
        onBlur?.(e);
      }}
      onKeyDown={handleKeyDown}
      {...restProps}
    ></div>
  );
});
