import { cn } from '@/utils';

interface TagProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
  highlight?: boolean;
  disabled?: boolean;
}

export function Tag({ children, onClick, className = '', style, highlight = false, disabled = false }: TagProps) {
  return (
    <span
      className={cn(
        'flex items-center justify-center h-5 px-2 rounded text-xs border',
        'select-none transition-all duration-100',
        {
          'text-white border-white/60': !highlight,
        },
        className,
        {
          'border-primary bg-primary/10 text-primary': highlight,
          'cursor-not-allowed opacity-70': disabled,
          'cursor-pointer': !disabled,
        },
      )}
      onClick={disabled ? undefined : onClick}
      style={style}
    >
      {children}
    </span>
  );
}

interface TagGroupProps {
  items: TagProps[];
  wrapperClassName?: string;
}

export function TagGroup({ items, wrapperClassName = '' }: TagGroupProps) {
  return (
    <div className={cn('flex flex-wrap gap-2', wrapperClassName)}>
      {items.map((item, index) => (
        <Tag key={typeof item.children === 'string' ? item.children : index} {...item} />
      ))}
    </div>
  );
}
