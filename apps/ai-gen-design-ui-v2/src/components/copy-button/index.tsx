import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MessagePlugin } from 'tdesign-react';

import { MESSAGE_PLUGIN_CONFIG } from '@/constant';

import Button, { ButtonProps } from '../button';
import SvgIcon from '../svg-icon';

export default function CopyButton({
  content,
  onDone,
  onError,
  feedback = 'message',
  ...restProps
}: {
  content: string | ClipboardItem | ClipboardItem[] | (() => string | ClipboardItem | ClipboardItem[]);
  onDone?: () => void;
  onError?: (err: any) => void;
  // 反馈方式
  feedback?: 'message' | 'inner';
} & ButtonProps) {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  const handleCopyPolyfill = (content: string) => {
    return new Promise<void>((resolve, reject) => {
      const textarea = document.createElement('textarea');
      textarea.value = content;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      textarea.setSelectionRange(0, textarea.value.length);
      const success = document.execCommand('copy');
      document.body.removeChild(textarea);
      if (success) resolve();
      else reject();
    });
  };

  const handleCopy = () => {
    let promise;
    const realContent = typeof content === 'function' ? content() : content;
    if (typeof realContent === 'string') {
      if (navigator.clipboard) {
        promise = navigator.clipboard.writeText(realContent);
      } else {
        promise = handleCopyPolyfill(realContent);
      }
    } else if (navigator.clipboard) {
      promise = navigator.clipboard.write(Array.isArray(realContent) ? realContent : [realContent]);
    }
    promise
      ?.then(() => {
        onDone?.();
      })
      .catch((err) => {
        onError?.(err);
      })
      .finally(() => {
        if (feedback === 'message') {
          MessagePlugin.success({
            content: t('copy-success'),
            ...MESSAGE_PLUGIN_CONFIG,
          });
        } else {
          setCopied(true);
          setTimeout(() => {
            setCopied(false);
          }, 2000);
        }
      });
  };

  return (
    <Button border={false} onClick={handleCopy} {...restProps}>
      {copied && t('copy-success')}
      <SvgIcon name="copy" />
    </Button>
  );
}
