import { ReactNode, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronRightIcon } from 'tdesign-icons-react';

import { cn } from '@/utils';

import Button, { ButtonProps } from '../button';
import LanguageButton from '../language-button';

export interface BreadcrumbItem {
  label: string;
  key: string;
  onClick?: (item: BreadcrumbItem) => void;
  active?: boolean;
  disabled?: boolean;
}

interface PageLayoutProps {
  title?: ReactNode;
  headerRender?: (languageElement: ReactNode, breadcrumbElement: ReactNode) => ReactNode;
  headerButtons?: ReactNode[];
  description?: ReactNode;
  footer?: ReactNode;
  footerRender?: (tipElement: ReactNode) => ReactNode;
  children?: ReactNode;
  bodyClassName?: string;
  onBack?: () => void;
  backButtonProps?: ButtonProps;
  breadcrumb?: BreadcrumbItem[];
}

export default function PageLayout({
  title,
  description,
  footer,
  children,
  bodyClassName,
  footerRender,
  headerRender,
  headerButtons,
  onBack,
  backButtonProps,
  breadcrumb,
}: PageLayoutProps) {
  const { t } = useTranslation();

  const tipElement = (
    <span className="text-xs text-white/40">
      {t('ai-tip')}（version: {import.meta.env.VITE_APP_VERSION}）
    </span>
  );
  const languageElement = <LanguageButton />;

  const breadcrumbElement = useMemo(() => {
    const items = breadcrumb
      ?.map((item, index, array) => [
        <div
          key={item.key}
          onClick={() => {
            if (item.disabled) return;
            item.onClick?.(item);
          }}
          className={cn('text-white/40 cursor-pointer transition-all duration-200', {
            'text-white': item.active,
            'hover:text-white': !item.disabled,
            'cursor-not-allowed': item.disabled,
          })}
        >
          {item.label}
        </div>,
        index < array.length - 1 && <ChevronRightIcon className="text-white/40 text-xl" />,
      ])
      .flat()
      .filter(Boolean);

    return items;
  }, [breadcrumb]);

  return (
    <div className="box-border size-full p-4 pb-0 flex flex-col bg-rootBg overflow-x-hidden">
      <div className="flex-shrink-0 mb-4">
        <div className="text-base font-bold text-white mb-2 flex items-center gap-2">
          {headerRender
            ? headerRender(languageElement, breadcrumbElement)
            : [
                onBack && <Button key="back" icon="home" onClick={onBack} border={false} {...backButtonProps}></Button>,
                title,
                breadcrumbElement,
                <div className="ml-auto flex gap-3" key="actions">
                  {headerButtons}
                  {languageElement}
                </div>,
              ]}
        </div>
        <div className="text-xs text-white/60 flex items-center gap-1">{description}</div>
      </div>

      <div className={cn('flex-1 min-h-0 overflow-y-auto custom-scrollbar', bodyClassName)}>{children}</div>

      <div className="flex-shrink-0 flex justify-between items-center h-12 bg-rootBg">
        {footerRender ? footerRender?.(tipElement) : [tipElement, footer]}
      </div>
    </div>
  );
}
