import clsx from 'clsx';
import { motion } from 'framer-motion';
import React, { ReactNode, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import * as ReactDOM from 'react-dom/client';

export interface MessageProps {
  children: ReactNode;
  visible?: boolean;
  defaultVisible?: boolean;
  /**
   * Message 显示的时间，单位毫秒，值为 -1 时一直显示
   * @default 3000
   */
  duration?: number;
  className?: string;
  style?: React.CSSProperties;
  onClose?: () => void;
  container?: HTMLElement;
  type?: 'common' | 'error';
}

const Message: React.FC<MessageProps> = ({
  children,
  visible: propVisible,
  defaultVisible = false,
  duration = 3000,
  className,
  style,
  onClose,
  container,
  type = 'common',
}) => {
  const isControlled = propVisible !== undefined;

  const [internalVisible, setInternalVisible] = useState(defaultVisible);

  const visible = isControlled ? propVisible : internalVisible;

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (duration !== -1 && visible) {
      timer = setTimeout(() => {
        if (!isControlled) {
          setInternalVisible(false);
        }
        onClose?.();
      }, duration);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [visible, duration, isControlled, onClose]);

  const typeStyles: Record<string, string> = {
    common: 'bg-primary/20 text-primary border-primary',
    error: 'bg-red-500/20 text-red-500 border-red-500',
  };

  return createPortal(
    visible && (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.2 }}
        className={clsx(
          'fixed top-10 left-0 right-0 mx-auto w-max text-xs px-4 py-3 rounded-md shadow-lg border',
          typeStyles[type],
          className,
        )}
        style={style}
      >
        {children}
      </motion.div>
    ),
    container ?? document.body,
  );
};

interface MessageAPI {
  show: (content: ReactNode, duration?: number, onClose?: () => void) => () => void;
  error: (content: ReactNode, duration?: number, onClose?: () => void) => () => void;
}

let messageContainer: HTMLDivElement | null = null;

const getMessageContainer = () => {
  if (!messageContainer) {
    messageContainer = document.createElement('div');
    messageContainer.className = 'message-container';
    document.body.appendChild(messageContainer);
  }
  return messageContainer;
};

const showMessage = (content: ReactNode, type: 'common' | 'error', duration = 3000, onClose?: () => void) => {
  const container = getMessageContainer();
  const messageDiv = document.createElement('div');
  container.appendChild(messageDiv);

  const root = ReactDOM.createRoot(messageDiv);
  let alreadytClose = false;

  const handleClose = () => {
    if (!alreadytClose) {
      alreadytClose = true;
      root.unmount();
      container.removeChild(messageDiv);
      onClose?.();
    }
  };

  root.render(
    <Message defaultVisible duration={duration} onClose={handleClose} container={container} type={type}>
      {content}
    </Message>,
  );

  return handleClose;
};

const messageAPI: MessageAPI = {
  show: (content, duration, onClose) => showMessage(content, 'common', duration, onClose),
  error: (content, duration, onClose) => showMessage(content, 'error', duration, onClose),
};

const MessageWithAPI = Object.assign(Message, messageAPI);

export default MessageWithAPI;
