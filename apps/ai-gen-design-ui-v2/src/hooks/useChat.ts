import { useXAgent } from '@ant-design/x';
import { XAgent } from '@ant-design/x/es/use-x-agent';
import { SimpleType } from '@ant-design/x/es/use-x-chat';
import { useMemoizedFn } from 'ahooks';
import { useRef, useState } from 'react';

import { request, RequestOptions, sseRequest } from '@/api';
import { IAgentMessage, IAgentMessageMetadata } from '@/type';

import useXChat, { MessageInfo } from './useXChat';

interface BaseChatOptions {
  /** 当前对话完成，包括成功和失败 */
  onChatComplete?: (message: IAgentMessage) => void;
  /** 当前对话成功 */
  onChatSuccess?: (message: IAgentMessage) => void;
  /** 当前对话失败 */
  onChatError?: (err: Error, message: IAgentMessage) => void;
  getFetchOptions:
    | { url: string; options?: RequestOptions }
    | ((
        isRegenerate: boolean,
        message?: IAgentMessage,
        messages?: IAgentMessage[],
      ) => { url: string; options?: RequestOptions });
}

interface TextChatOptions extends BaseChatOptions {
  stream?: false;
}
interface StreamChatOptions extends BaseChatOptions {
  stream: true;
}
interface ReturnType<T extends SimpleType> {
  agent: XAgent<T>;
  onRequest: (message: T, isRegenerate: boolean) => void;
  messages: MessageInfo<T>[];
  setMessages: (messages: MessageInfo<T>[]) => void;
  latestMessageMetadata: IAgentMessageMetadata | undefined;
  isRequesting: boolean;
  abortChatRequest: () => void;
}

export function useChat(options: TextChatOptions | StreamChatOptions): ReturnType<IAgentMessage> {
  const { onChatComplete, onChatSuccess, onChatError } = options;
  const abortControllerRef = useRef<AbortController>();

  const [latestMessageMetadata, setLatestMessageMetadata] = useState<IAgentMessageMetadata>();
  const [isRequesting, setIsRequesting] = useState(false);

  const abortChatRequest = useMemoizedFn(() => {
    setIsRequesting(false);
    if (!abortControllerRef.current?.signal?.aborted) abortControllerRef.current?.abort();
  });

  const [agent] = useXAgent<IAgentMessage>({
    request: async ({ message, messages, isRegenerate }, xAgentRequestCallbacks) => {
      setIsRequesting(true);
      abortControllerRef.current = new AbortController();
      const currentMessage: IAgentMessage = {
        content: '',
        thinkContent: '',
        metadata: undefined,
      };
      let thinking: boolean | null = null;

      const triggerSuccess = () => {
        xAgentRequestCallbacks.onSuccess(currentMessage);
        onChatSuccess?.(currentMessage);
        onChatComplete?.(currentMessage);
      };
      const triggerError = (err: Error) => {
        onChatComplete?.(currentMessage);
        if (abortControllerRef.current?.signal?.aborted && err.message.includes('aborted')) {
          xAgentRequestCallbacks.onSuccess(currentMessage);
          return;
        }
        xAgentRequestCallbacks.onError(err);
        onChatError?.(err, currentMessage);
      };
      const triggerUpdate = () => {
        xAgentRequestCallbacks.onUpdate(currentMessage);
      };

      const { url, options: fetchOptions } =
        typeof options.getFetchOptions === 'function'
          ? options.getFetchOptions(isRegenerate, message, messages)
          : options.getFetchOptions;

      try {
        if (options.stream) {
          await sseRequest(
            url,
            { ...fetchOptions, signal: abortControllerRef.current?.signal },
            {
              onMessage: (chunk) => {
                if (abortControllerRef.current?.signal?.aborted) return;
                if (!chunk) return;

                let parsedData = {} as any;
                try {
                  parsedData = JSON.parse(chunk);
                } catch (error) {
                  console.error('解析 sse 数据失败', error);
                }

                if (parsedData.type === '[START]') {
                  currentMessage.metadata = parsedData.metadata;
                  setLatestMessageMetadata(currentMessage.metadata);
                  triggerUpdate();
                } else if (parsedData.type === '[TEXT]') {
                  const text = parsedData.content;
                  if (thinking === true) {
                    const thinkPart = '\n</think>\n';
                    currentMessage.thinkContent += thinkPart;
                    currentMessage.content += thinkPart;
                    thinking = false;
                  }
                  currentMessage.content += text;
                  triggerUpdate();
                } else if (parsedData.type === '[REASONING]') {
                  const thinkPart = thinking === null ? `<think>\n${parsedData.content}` : parsedData.content;
                  currentMessage.thinkContent += thinkPart;
                  currentMessage.content += thinkPart;
                  thinking = true;
                  triggerUpdate();
                } else if (parsedData.type === '[ERROR]') {
                  triggerError(new Error(parsedData.error));
                } else if (parsedData.type === '[DONE]') {
                  triggerSuccess();
                }
              },
              onError: (err) => {
                triggerError(err);
              },
              onComplete: () => {
                triggerSuccess();
                setIsRequesting(false);
              },
            },
          );
        } else {
          const response = await request(url, { ...fetchOptions, signal: abortControllerRef.current?.signal });
          if (!response?.ok) {
            triggerError(new Error(response?.statusText || '请求对话接口失败'));
            return;
          }
          const data = await response.json();
          // TODO 数据处理不完善
          currentMessage.content = data.content;
          triggerSuccess();
          setIsRequesting(false);
        }
      } catch (err) {
        triggerError(new Error((err as Error).message || '请求对话接口失败'));
        setIsRequesting(false);
      }
    },
  });

  const { onRequest, messages, setMessages } = useXChat({
    agent,
    requestPlaceholder: {
      content: '',
    },
    requestFallback: (message, { error }) => {
      return {
        loading: false,
        content: error.message,
      };
    },
  });

  return { agent, onRequest, messages, setMessages, latestMessageMetadata, isRequesting, abortChatRequest };
}
