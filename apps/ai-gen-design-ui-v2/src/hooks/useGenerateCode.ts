import { produce } from 'immer';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MessagePlugin, NotificationPlugin } from 'tdesign-react';

import { generateCode, GenerateCodeRequestBody, getCodeList, stopGenerateCode } from '@/api';
import { MESSAGE_PLUGIN_CONFIG } from '@/constant';
import { useAIStore } from '@/store';
import { PageArchitecture, PageCode } from '@/type';

export function useGenerateCode() {
  const [pageCodes, setPageCodes] = useState<PageCode[]>([]);
  const [currentPageCodeIndex, setCurrentPageCodeIndex] = useState<number>(0);
  const [isGenerating, setIsGenerating] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);
  const { t } = useTranslation();

  const runGenerateSingleTask = async (body: GenerateCodeRequestBody): Promise<string | null> => {
    const { targetPageId } = body;

    try {
      abortControllerRef.current = new AbortController();
      const response = await generateCode(body, abortControllerRef.current);

      if (!response.ok) {
        throw new Error(`请求错误: ${response.status}`);
      }
      const result = (await response.json()).data;
      if (result.success) {
        setPageCodes((prev) => {
          const index = prev.findIndex((p) => p.pageId === targetPageId);
          if (index === -1) return prev;

          return produce(prev, (draft) => {
            draft[index].success = true;
            draft[index].code = result.html;
            draft[index].codeId = result.codeId;
            draft[index].completed = true;
            draft[index].generating = false;
          });
        });
        MessagePlugin.success({ content: `${result.pageName} ${t('success')}`, ...MESSAGE_PLUGIN_CONFIG });
        return result.codeId;
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      if (error.name === 'AbortError') return null;
      setPageCodes((prev) => {
        const index = prev.findIndex((p) => p.pageId === targetPageId);
        if (index === -1) return prev;

        return produce(prev, (draft) => {
          draft[index].success = false;
          draft[index].error = error.message || '生成失败';
          draft[index].completed = true;
          draft[index].generating = false;
        });
      });
      NotificationPlugin.error({
        title: t('generate.failed'),
        content: typeof error === 'string' ? error : error.message,
        closeBtn: true,
      });
      return null;
    }
  };

  const runGenerateTask = async (architectureMessageId: string, partialPageArchitecture: PageArchitecture) => {
    setIsGenerating(true);

    const pages = partialPageArchitecture.pages || [];

    if (pages.length === 0) {
      MessagePlugin.error({ content: t('generate.no-page-selected'), ...MESSAGE_PLUGIN_CONFIG });
      setIsGenerating(false);
      return;
    }

    const newPageCodes = pages.map((p) => ({
      pageName: p.n!,
      pageId: p.id!,
      generating: false,
      completed: false,
    }));

    setPageCodes(newPageCodes);
    setCurrentPageCodeIndex(0);

    const { pageType, getGenerateCodeContext } = useAIStore.getState();
    const requestBody: Omit<GenerateCodeRequestBody, 'targetPageId' | 'previousGeneratedCodes'> = {
      pageArchitecture: partialPageArchitecture,
      pageType,
      architectureMessageId,
      context: await getGenerateCodeContext(partialPageArchitecture),
    };

    const previousGeneratedCodes = [];

    abortControllerRef.current = new AbortController();

    for (let i = 0; i < pages.length; i++) {
      if (abortControllerRef.current?.signal.aborted) {
        break;
      }

      const page = pages[i];

      const currentPageCode = { ...newPageCodes[i], generating: true };
      setPageCodes((prev) => {
        return produce(prev, (draft) => {
          Object.assign(draft[i], currentPageCode);
        });
      });

      const currentCodeId = await runGenerateSingleTask({
        ...requestBody,
        targetPageId: page.id!,
        previousGeneratedCodes: previousGeneratedCodes,
      });
      setCurrentPageCodeIndex(i);

      if (currentCodeId) {
        previousGeneratedCodes.push(currentCodeId);
      }
    }

    setIsGenerating(false);
  };

  const stopGenerateTask = (architectureMessageId: string) => {
    abortControllerRef.current?.abort();
    setIsGenerating(false);
    stopGenerateCode(architectureMessageId);
  };

  const requestCodeList = async (architectureMessageId: string) => {
    const res = await getCodeList(architectureMessageId);
    const resObj = await res.json();
    const mapData = resObj.data?.map((o: any) => ({
      pageName: o.pageName,
      pageId: o.pageId,
      codeId: o.codeId,
      code: o.code,
      completed: true,
      success: true,
    }));
    setPageCodes(mapData);
    setCurrentPageCodeIndex(0);
  };

  return {
    pageCodes,
    currentPageCodeIndex,
    isGenerating,
    currentPageCode: pageCodes[currentPageCodeIndex],
    setCurrentPageCodeIndex,
    runGenerateTask,
    stopGenerateTask,
    requestCodeList,
  };
}
