import { useLatest, useSafeState } from 'ahooks';

/**
 * 用法和useState一样，不过如果valueFromProps有值，则不使用内部state，使用valueFromProps
 * 通常用来在组件内实现受控、非受控两种使用方式
 */
export function useMergedState<T>(
  defaultValue?: T,
  valueFromProps: {
    value?: T;
    defaultValue?: T;
    onValueChange?: (value: T) => void;
  } = {},
) {
  const { value: propsValue, defaultValue: propsDefaultValue, onValueChange: propsOnValueChange } = valueFromProps;
  const propsValueExist = useLatest(propsValue !== undefined);

  const [internalValue, setInternalValue] = useSafeState(() => {
    if (propsValueExist.current) return propsValue;
    if (propsDefaultValue !== undefined) return propsDefaultValue;
    return defaultValue;
  });

  const mergedState = propsValueExist.current ? propsValue : internalValue;

  const internalSetValue = (val: T | ((prev: T) => T)) => {
    if (Object.is(val, mergedState)) return;

    if (!propsValueExist.current) {
      setInternalValue(val as T);
    }
    propsOnValueChange?.(val as T);
  };

  return [mergedState, internalSetValue] as const;
}
