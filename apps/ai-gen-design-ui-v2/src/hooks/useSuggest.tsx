import { useDebounceFn, useMemoizedFn, usePrevious, useUnmount } from 'ahooks';
import { RefObject, useCallback, useEffect, useRef, useState } from 'react';
import useSWRMutation from 'swr/mutation';

import { request } from '@/api';
import { convertInnerHtmlToText, moveCaretToEnd } from '@/components/editable-div/helper';
import { AI_GEN_DESIGN_URL_PREFIX, TRACK_ACTION } from '@/constant';
import { track } from '@/utils';

function useSuggestApi({
  referenceContent,
  requestBody,
}: {
  referenceContent?: string;
  requestBody?: Record<string, any> | (() => Record<string, any>);
}) {
  const abortController = useRef<AbortController>();
  const prevReferenceContent = usePrevious(referenceContent);
  const isMounted = useRef(false);

  const abortRequest = useMemoizedFn(() => {
    if (abortController.current) {
      abortController.current.abort('suggest abort');
    }
  });

  useUnmount(() => {
    abortRequest();
  });

  const { data, trigger, isMutating } = useSWRMutation(
    [`${AI_GEN_DESIGN_URL_PREFIX}/suggest`, referenceContent],
    async ([url]) => {
      abortRequest();
      abortController.current = new AbortController();

      const res = await request(url, {
        method: 'POST',
        signal: abortController.current.signal,
        body: {
          prompt: referenceContent,
          ...(typeof requestBody === 'function' ? requestBody() : requestBody),
        },
      });
      const data = (await res.json())?.data;
      if (!data || data.includes('null')) {
        return undefined;
      }

      return data.replace(/^["''"]|["''"]$/g, '');
    },
  );
  const { run: debouncedTrigger } = useDebounceFn(trigger, { wait: 300, leading: true });

  useEffect(() => {
    if (isMounted.current) {
      const trimedRC = referenceContent?.trim();
      if (trimedRC && prevReferenceContent?.trim() !== trimedRC) debouncedTrigger();
    }
    isMounted.current = true;
  }, [referenceContent]);

  return { data, isMutating, abortRequest };
}

export default function useSuggest<T extends HTMLElement>(option: {
  inputRef: RefObject<T>;
  referenceContent: string;
  onApply?: (originValueWithSuggestion: string) => void;
  requestBody?: Record<string, any> | (() => Record<string, any>);
}) {
  const { onApply, referenceContent, requestBody, inputRef } = option ?? {};

  const isComposing = useRef(false);

  const [suggest, setSuggest] = useState<{
    show: boolean;
    content?: string;
  }>({ show: false, content: '' });

  const { data: suggestion, abortRequest } = useSuggestApi({
    referenceContent,
    requestBody,
  });

  useEffect(() => {
    showSuggest(suggestion);
  }, [suggestion]);

  useEffect(() => {
    destroySuggest();
  }, [referenceContent]);

  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        e.preventDefault();
        let curInnerHTML = inputRef.current!.innerHTML;
        if (suggest.show && curInnerHTML.includes('<span')) {
          track.send(TRACK_ACTION['adopt-suggestion']);
          curInnerHTML = curInnerHTML.slice(0, curInnerHTML.indexOf('<span'));
          onApply?.(`${convertInnerHtmlToText(curInnerHTML)}${suggest.content}`);
        }
      }
      destroySuggest();
    };
    const handleBlur = () => {
      destroySuggest();
    };
    const handleMousedown = () => {
      destroySuggest();
    };
    const handleCompositionstart = () => {
      isComposing.current = true;
    };
    const handleCompositionend = () => {
      isComposing.current = false;
    };

    inputRef.current?.addEventListener('keydown', handleKeydown);
    inputRef.current?.addEventListener('blur', handleBlur);
    inputRef.current?.addEventListener('mousedown', handleMousedown);
    inputRef.current?.addEventListener('compositionstart', handleCompositionstart);
    inputRef.current?.addEventListener('compositionend', handleCompositionend);
    return () => {
      inputRef.current?.removeEventListener('keydown', handleKeydown);
      inputRef.current?.removeEventListener('blur', handleBlur);
      inputRef.current?.removeEventListener('mousedown', handleMousedown);
      inputRef.current?.removeEventListener('compositionstart', handleCompositionstart);
      inputRef.current?.removeEventListener('compositionend', handleCompositionend);
    };
  }, [onApply, suggest]);

  const showSuggest = useCallback((text: string) => {
    if (!inputRef.current || document.activeElement !== inputRef.current || isComposing.current) return;

    let curInnerHTML = inputRef.current.innerHTML;
    const alreadyHasSuggest = curInnerHTML.includes('<span');

    // 如果存在 suggest，则删除之前的 suggest
    if (alreadyHasSuggest) {
      curInnerHTML = curInnerHTML.slice(0, curInnerHTML.indexOf('<span'));
    }

    // 保存当前选区
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);

    // 更新 innerHTML
    inputRef.current.innerHTML = `${curInnerHTML ?? ''}${getSuggestHTML(text)}`;

    // 恢复光标位置到suggest之前
    if (range && selection) {
      const newRange = document.createRange();
      const textNodes = Array.from(inputRef.current.childNodes).filter((node) => node.nodeType === Node.TEXT_NODE);
      const lastTextNode = textNodes[textNodes.length - 1];

      if (lastTextNode) {
        // 将光标设置在最后一个文本节点的末尾
        newRange.setStart(lastTextNode, lastTextNode.textContent?.length || 0);
      } else {
        // 如果没有文本节点，则将光标设置在容器开始位置
        newRange.setStart(inputRef.current, 0);
      }

      newRange.collapse(true);
      selection.removeAllRanges();
      selection.addRange(newRange);
    }

    setSuggest({ show: true, content: text });
  }, []);

  const destroySuggest = useCallback(() => {
    if (!suggest.show) return;

    let curInnerHTML = inputRef.current!.innerHTML;
    const alreadyHasSuggest = curInnerHTML.includes('<span');
    // 如果存在 suggest，则删除之前的 suggest
    if (alreadyHasSuggest) {
      curInnerHTML = curInnerHTML.slice(0, curInnerHTML.indexOf('<span'));
    }
    inputRef.current!.innerHTML = curInnerHTML;
    moveCaretToEnd(inputRef.current!);

    setSuggest({ show: false, content: '' });
  }, [suggest.show]);

  const getSuggestHTML = (content: string) => {
    return `<span class='select-none pointer-events-none text-white/50'>  ${content}</span>`;
  };

  /** 去除传入的text中的suggest */
  const removeSuggestFormat = (valueHtml: string) => {
    if (suggest.show && valueHtml.includes('<span')) {
      valueHtml = valueHtml.slice(0, valueHtml.indexOf('<span'));
    }
    return valueHtml;
  };

  return {
    suggest,
    showSuggest,
    destroySuggest,
    removeSuggestFormat,
    abortRequest,
  };
}
