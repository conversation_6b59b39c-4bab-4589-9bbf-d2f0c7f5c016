import { Locales } from '@/locale';

import { DesignStyleKeyword, PageType } from './type';

export const AI_GEN_DESIGN_URL_PREFIX = '/ai-gen-design/v2';
export const API_PREFIX = import.meta.env.VITE_PLUGIN_SERVER_URL;

export const HEADERS_USER = 'X-User-Id';

export const PageTypes: { label: Record<Locales, string>; value: PageType }[] = [
  { label: { [Locales.EN_US]: 'Mobile', [Locales.ZH_CN]: '移动端' }, value: PageType.app },
  { label: { [Locales.EN_US]: 'Web', [Locales.ZH_CN]: '网页端' }, value: PageType.pc },
  // { label: { [Locales.EN_US]: 'Console', [Locales.ZH_CN]: '控制台' }, value: PageType.console },
];

export const PromptExamples: { pageType: PageType; content: Record<Locales, string> }[] = [
  {
    pageType: PageType.app,
    content: {
      [Locales.EN_US]: 'movie detail page',
      [Locales.ZH_CN]: '电影详情页面',
    },
  },
  {
    pageType: PageType.app,
    content: {
      [Locales.EN_US]: 'music app',
      [Locales.ZH_CN]: '音乐APP',
    },
  },
  {
    pageType: PageType.app,
    content: {
      [Locales.EN_US]: 'pet products app',
      [Locales.ZH_CN]: '宠物用品商城APP',
    },
  },
  {
    pageType: PageType.app,
    content: {
      [Locales.EN_US]: 'social media app',
      [Locales.ZH_CN]: '社交媒体类APP',
    },
  },
  {
    pageType: PageType.app,
    content: {
      [Locales.EN_US]: 'car manufacturer app',
      [Locales.ZH_CN]: '车企APP',
    },
  },
  {
    pageType: PageType.app,
    content: {
      [Locales.EN_US]: 'podcast app homepage',
      [Locales.ZH_CN]: '播客APP的首页',
    },
  },
  {
    pageType: PageType.pc,
    content: {
      [Locales.EN_US]: 'official website of SaaS tools',
      [Locales.ZH_CN]: 'SaaS工具的官网',
    },
  },
  {
    pageType: PageType.pc,
    content: {
      [Locales.EN_US]: 'personal blog',
      [Locales.ZH_CN]: '个人博客',
    },
  },
  {
    pageType: PageType.pc,
    content: {
      [Locales.EN_US]: 'e-commerce platform',
      [Locales.ZH_CN]: '类似小米官网的电商网站',
    },
  },
  {
    pageType: PageType.pc,
    content: {
      [Locales.EN_US]: 'system overview console, including important statistics tables',
      [Locales.ZH_CN]: '系统总览控制台',
    },
  },
  {
    pageType: PageType.pc,
    content: {
      [Locales.EN_US]: 'game community website',
      [Locales.ZH_CN]: '游戏社区官网',
    },
  },
  {
    pageType: PageType.pc,
    content: {
      [Locales.EN_US]: 'movie website',
      [Locales.ZH_CN]: '电影网站',
    },
  },
];

export const DesignStyleKeywords: DesignStyleKeyword[] = [
  { key: 'light', value: { [Locales.EN_US]: 'light', [Locales.ZH_CN]: '浅色' } },
  { key: 'dark', value: { [Locales.EN_US]: 'dark', [Locales.ZH_CN]: '暗色' } },
  { key: 'simple', value: { [Locales.EN_US]: 'simple', [Locales.ZH_CN]: '简约' } },
  { key: 'corporate', value: { [Locales.EN_US]: 'corporate', [Locales.ZH_CN]: '企业' } },
  { key: 'imitation', value: { [Locales.EN_US]: 'imitation', [Locales.ZH_CN]: '拟态' } },
  { key: 'artistic', value: { [Locales.EN_US]: 'artistic', [Locales.ZH_CN]: '艺术' } },
  { key: 'tech', value: { [Locales.EN_US]: 'tech', [Locales.ZH_CN]: '科技感' } },
  { key: 'active', value: { [Locales.EN_US]: 'active', [Locales.ZH_CN]: '活泼的' } },
  { key: 'fashion', value: { [Locales.EN_US]: 'fashion', [Locales.ZH_CN]: '时尚的' } },
];

export const APP_WIDTH = 412;
export const WEB_WIDTH = 1920;

export const MESSAGE_PLUGIN_CONFIG = {
  className: 'h-8',
  offset: [0, -15],
};
export const ClientStoryKeys = {
  uid: 'AI_GEN_DESIGN_UI_UID',
};

export const TRACK_ACTION = {
  /** 请求后端API */
  'request-backend': 'request-backend',
  /** 采纳建议 */
  'adopt-suggestion': 'adopt-suggestion',
  /** 更新页面架构 */
  'update-request-page-architecture': 'update-request-page-architecture',
  /** 新请求页面架构 */
  'new-request-page-architecture': 'new-request-page-architecture',
  /** 请求页面架构失败 */
  'request-page-architecture-error': 'request-page-architecture-error',
  /** 手动中止请求页面架构 */
  'abort-request-page-architecture': 'abort-request-page-architecture',
  /** 上传参考图片 */
  'upload-reference-image': 'upload-reference-image',
  /** 开始生成代码 */
  'start-generate-code': 'start-generate-code',
  /** 手动中止生成代码 */
  'abort-generate-code': 'abort-generate-code',
  /** 生成代码失败 */
  'generate-code-error': 'generate-code-error',
  /** 拖拽页面区块 */
  'drag-section': 'drag-section',
  /** 通过手动输入的方式更新页面区块 */
  'input-update-section': 'input-update-section',
  /** 点赞生成结果 */
  'like-generate-result': 'like-generate-result',
  /** 踩生成结果 */
  'dislike-generate-result': 'dislike-generate-result',
  /** 插入画布 */
  'insert-canvas': 'insert-canvas',
  /** 插入画布失败 */
  'insert-canvas-error': 'insert-canvas-error',
  /** 点击预览代码 */
  'click-preview-code': 'click-preview-code',
  /** 下载代码 */
  'click-download-code': 'click-download-code',
};
