{"include": ["src/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx"], "compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "Node", "useDefineForClassFields": true, "skipLibCheck": true, "esModuleInterop": true, "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": false, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"]}}}