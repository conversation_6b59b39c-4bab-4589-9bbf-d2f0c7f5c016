import { MessageTypes } from '@ai-assitant/ai-core';

import { figmaMessages } from '@/utils';

export default async function getUser(): Promise<{ userId: string; userName: string }> {
  const noneUser = { userId: '', userName: '' };
  const devUser = { userId: 'local-dev', userName: 'local-dev' };
  try {
    if (import.meta.env.DEV) {
      return devUser;
    }

    const figmaCurrentUser = ((await figmaMessages[MessageTypes.GET_PLATFORM_USER].request({})) as any)?.data;
    return figmaCurrentUser;
  } catch (error) {
    return noneUser;
  }
}
