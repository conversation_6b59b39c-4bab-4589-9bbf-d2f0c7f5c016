import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';

import { API_PREFIX, HEADERS_USER, TRACK_ACTION } from '@/constant';

import getUser from './get-user';
import track from './track';

interface SseRequestOptions {
  method?: 'POST' | 'GET';
  body?: Record<string, any>;
  headers?: Record<string, any>;
  signal?: AbortSignal;
  onMessage?: (e: EventSourceMessage) => void;
  onClose?: () => void;
  onError?: (err: any, fromMessage: boolean) => void;
}

export async function sseRequest(url: string, options: SseRequestOptions = {}) {
  track.send(TRACK_ACTION['request-backend'], {
    eventValue: { url, method: options.method ?? 'POST' },
  });
  const headers = {
    'Content-Type': 'application/json',
    [HEADERS_USER]: (await getUser()).userId,
    ...options.headers,
  };
  return fetchEventSource(`${API_PREFIX}${url}`, {
    method: options.method ?? 'POST',
    body: JSON.stringify(options.body),
    headers,
    openWhenHidden: true,
    signal: options.signal,
    onerror: (error) => {
      options.onError?.(error.message, false);
      throw error;
    },
    onmessage: (msg: EventSourceMessage) => {
      if (msg.data === '') return;
      options.onMessage?.(msg);

      if (msg.event === 'error') {
        options.onError?.(msg.data, true);
      }
    },
    onclose: options.onClose,
  });
}

export async function request(url: string, options: Omit<RequestInit, 'body'> & { body?: Record<string, any> } = {}) {
  track.send(TRACK_ACTION['request-backend'], {
    eventValue: { url, method: options.method ?? 'POST' },
  });
  const headers = {
    'Content-Type': 'application/json',
    [HEADERS_USER]: (await getUser()).userId,
    ...options.headers,
  };
  const response = fetch(`${API_PREFIX}${url}`, {
    ...options,
    headers,
    body: options.body ? JSON.stringify(options.body) : undefined,
  });
  return response;
}
