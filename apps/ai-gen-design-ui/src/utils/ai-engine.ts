import { CVImage, InferenceEngine } from 'inferencejs';

type ComponentType = 'button' | 'other';

class AiEngine {
  private engines: any = {
    button: {
      workerId: null as any,
      modelName: 'ui-cing',
      version: '8',
      publishableKey: 'rf_5w20VzQObTXjJhTjq6kad9ubrm33',
    },
  };

  private inferEngine = new InferenceEngine();
  public constructor() {

  }

  // 加载模型
  loadModelPromise = (type: ComponentType) => {
    return new Promise((resolve, reject) => {
      const loadModel = async () => {
        try {
          const engine = this.engines[type];
          if (!engine) {
            resolve(null);
            return;
          }
          if (engine.workerId) {
            resolve(engine.workerId);
            return;
          }
          const id = await this.inferEngine.startWorker(engine.modelName, engine.version, engine.publishableKey);
          engine.workerId = id;
          resolve(id);
        } catch (error) {
          console.error('loadModelPromise 失败', error);
          reject(error);
        }
      };
      loadModel();
    });
  };

  // 创建一个图片元素
  createImagePromise = (url: string) => (
    new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      img.src = url;
      img.onload = () => {
        console.log('我加载了');
        resolve(img);
      };
      img.onerror = (e) => {
        console.error('图片加载失败', e);
        reject(null);
      };
    })
  );

  // 开始识别图片
  detectFrame = async ({ type, url }: { type: ComponentType; url: string }) => {
    const loadModel = this.loadModelPromise(type);
    const createImage = this.createImagePromise(url);
    const [workerId, img] = await Promise.all([loadModel, createImage]);

    if (!workerId || !img) {
      return;
    }

    const image = new CVImage(img as HTMLImageElement);
    const predictions = await this.inferEngine.infer(workerId as string, image);
    return predictions;
  };
}

const aiEngine = new AiEngine();

export default aiEngine;
