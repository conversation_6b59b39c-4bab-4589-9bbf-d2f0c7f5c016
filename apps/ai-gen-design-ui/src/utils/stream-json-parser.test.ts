import { describe, expect, it } from 'vitest';

import { StreamJSONParser } from './stream-json-parser';

describe('StreamJSONParser', () => {
  // 测试数据
  const testData = {
    object: {
      string: `{"name": "小美",
        "descrition":"一个女生",
        "age": -123,
        "height": 3.14159,
        "weight": 1e-10,
        "score": 1.23e+5,
        "friends":[{"name":"小帅",
        "descrition":"一个男生"},    
          {"name":"小丑","descrition":"一个小丑"}]}`,
      value: {
        name: '小美',
        descrition: '一个女生',
        age: -123,
        height: 3.14159,
        weight: 1e-10,
        score: 1.23e5,
        friends: [
          { name: '小帅', descrition: '一个男生' },
          { name: '小丑', descrition: '一个小丑' },
        ],
      },
    },
    array: {
      string: '[{"name":"小帅","descrition":"一个男生"},{"name":"小丑","descrition":"一个小丑"}]',
      value: [
        { name: '小帅', descrition: '一个男生' },
        { name: '小丑', descrition: '一个小丑' },
      ],
    },
  };

  const testParsing = (
    input: string,
    expectedValue: any,
    options: {
      chunkSize?: number;
      onPartial?: (value: any) => void;
    } = {},
  ) => {
    const parser = new StreamJSONParser((value, isComplete) => {
      if (isComplete) {
        expect(value).toEqual(expectedValue);
      } else {
        options.onPartial?.(value);
      }
    });

    const chunks = input.match(new RegExp(`.{1,${options.chunkSize ?? 2}}`, 'g')) || [];

    chunks.forEach(chunk => parser.feed(chunk));
    expect(parser.isComplete).toBe(true);
  };

  it('解析Object', () => {
    testParsing(testData.object.string, testData.object.value, {
      chunkSize: 2,
    });
  });

  it('解析Array', () => {
    testParsing(testData.array.string, testData.array.value);
  });

  it('JSON前后都有冗余内容', () => {
    const wrapper = (str: string) => `让我们来开始生成json内容吧，
      \`\`\`json
      ${str}
      \`\`\`
      以上就是我为您生成的内容`;

    testParsing(wrapper(testData.object.string), testData.object.value);
  });
});

describe('StreamJSONParser - 边界情况测试', () => {
  it('解析包含转义字符的字符串', () => {
    const testStr = '{"text": "Hello\\nWorld\\"Test\\\\"}';
    const expected = { text: 'Hello\nWorld"Test\\' };

    const parser = new StreamJSONParser((value, isComplete) => {
      if (isComplete) {
        expect(value).toEqual(expected);
      }
    });
    parser.feed(testStr);
  });

  it('处理空输入', () => {
    const parser = new StreamJSONParser();
    parser.feed(undefined as any);
    parser.feed('');
    parser.feed('   ');
    expect(parser.isComplete).toBe(false);
  });

  it('测试 reset 功能', () => {
    const parser = new StreamJSONParser();
    parser.feed('{"name": "test"}');
    expect(parser.isComplete).toBe(true);

    parser.reset();
    expect(parser.isComplete).toBe(false);
    expect(parser.buffer).toBe('');

    // reset 后可以重新解析
    parser.feed('{"name": "new test"}');
    expect(parser.isComplete).toBe(true);
  });

  it('错误处理 - 重复feed已完成的解析器', () => {
    let errorCaught = false;
    const parser = new StreamJSONParser(undefined, () => {
      errorCaught = true;
    });

    parser.feed('{"name": "test"}');
    parser.feed('{"name": "another"}');
    expect(errorCaught).toBe(true);
  });

  it('解析不完整的对象', () => {
    const chunks = ['{"name": "test"', ', "age": 25', '}'];

    const parser = new StreamJSONParser((value, isComplete) => {
      if (isComplete) {
        expect(value).toEqual({ name: 'test', age: 25 });
      } else if (value) {
        // 部分解析的结果
        expect(value).toHaveProperty('name', 'test');
      }
    });

    chunks.forEach(chunk => parser.feed(chunk));
  });

  it('带转义的字符串解析集合', () => {
    const stringTests = [
      {
        input: '{"str": "Hello\\\\World"}',
        expected: { str: 'Hello\\World' },
        desc: '转义反斜杠',
      },
      {
        input: '{"str": "Hello\\n\\t\\r\\"World"}',
        expected: { str: 'Hello\n\t\r"World' },
        desc: '转义引号、换行、制表符、回车',
      },
      {
        input: '{"str": "Hello\\u0041World"}',
        expected: { str: 'HelloAWorld' },
        desc: 'Unicode转义',
      },
      {
        input: '{"str": "\\\\\\"\\n\\r\\t"}',
        expected: { str: '\\"\\n\\r\\t' },
        desc: '多重转义',
      },
    ];

    stringTests.forEach(({ input, expected }) => {
      const parser = new StreamJSONParser((value, isComplete) => {
        if (isComplete) {
          expect(value).toEqual(expected);
        }
      });
      parser.feed(input);
      expect(parser.isComplete).toBe(true);
    });
  });

  it('数字解析集合', () => {
    const numberTests = [
      { input: '{"val": 0}', expected: { val: 0 }, desc: '零' },
      { input: '{"val": -0}', expected: { val: -0 }, desc: '负零' },
      { input: '{"val": 9007199254740991}', expected: { val: Number.MAX_SAFE_INTEGER }, desc: '最大安全整数' },
      { input: '{"val": -9007199254740991}', expected: { val: -Number.MAX_SAFE_INTEGER }, desc: '最小安全整数' },
      { input: '{"val": 0.000000000001}', expected: { val: 0.000000000001 }, desc: '极小小数' },
      { input: '{"val": 1.7976931348623157e+308}', expected: { val: Number.MAX_VALUE }, desc: '最大数值' },
      { input: '{"val": 5e-324}', expected: { val: Number.MIN_VALUE }, desc: '最小数值' },
      { input: '{"val": 1.23456789e-10}', expected: { val: 1.23456789e-10 }, desc: '科学计数法小数' },
      { input: '{"val": -1.23456789e+10}', expected: { val: -1.23456789e10 }, desc: '科学计数法负数' },
    ];

    numberTests.forEach(({ input, expected }) => {
      const parser = new StreamJSONParser((value, isComplete) => {
        if (isComplete) {
          expect(value).toEqual(expected);
          expect(typeof value.val).toBe('number');
          if (!Number.isInteger(expected.val)) {
            expect(value.val.toPrecision()).toBe(expected.val.toPrecision());
          }
        }
      });
      parser.feed(input);
      expect(parser.isComplete).toBe(true);
    });
  });
});

// const chunks = [
//   '<th',
//   'ink',
//   '>\n好的，',
//   '我现',
//   '在需',
//   '要帮',
//   '用户',
//   '分析',
//   'Fig',
//   'ma社',
//   '区里',
//   '的这',
//   '个AI',
//   ' De',
//   'sig',
//   'n R',
//   'evi',
//   'ewe',
//   'r插',
//   '件。',
//   '用户',
//   '给了',
//   '一个',
//   '链接',
//   '，还',
//   '提供',
//   '了多',
//   '个搜',
//   '索结',
//   '果，',
//   '我需',
//   '要结',
//   '合这',
//   '些信',
//   '息来',
//   '生成',
//   '回答',
//   '。\n',
//   '首先',
//   '，我',
//   '要确',
//   '认这',
//   '个插',
//   '件的',
//   '主要',
//   '功能',
//   '。根',
//   '据搜',
//   '索结',
//   '果的',
//   '文献',
//   '4、',
//   '5、',
//   '6、',
//   '7，',
//   '这个',
//   '插件',
//   '是F',
//   'igm',
//   'a的',
//   '一个',
//   '工具',
//   '，用',
//   '于检',
//   '查设',
//   '计中',
//   '的问',
//   '题，',
//   '优化',
//   'UI/',
//   'UX、',
//   '可访',
//   '问性',
//   '、C',
//   'TA',
//   '和文',
//   '案。',
//   '文献',
//   '4提',
//   '到它',
//   '可以',
//   '帮助',
//   '提升',
//   '转化',
//   '率，',
//   '优化',
//   '按钮',
//   '和文',
//   '案，',
//   '符合',
//   'WCA',
//   'G标',
//   '准。',
//   '文献',
//   '5和',
//   '7详',
//   '细描',
//   '述了',
//   '它的',
//   '功能',
//   '，比',
//   '如审',
//   '核界',
//   '面、',
//   '改进',
//   '文案',
//   '、确',
//   '保可',
//   '访问',
//   '性等',
//   '。\n',
//   '接下',
//   '来，',
//   '我需',
//   '要整',
//   '理这',
//   '些信',
//   '息，',
//   '看看',
//   '有没',
//   '有冲',
//   '突的',
//   '地方',
//   '。比',
//   '如，',
//   '不同',
//   '文献',
//   '是否',
//   '提到',
//   '相同',
//   '的功',
//   '能？',
//   '看起',
//   '来文',
//   '献4',
//   '、5',
//   '、6',
//   '、7',
//   '内容',
//   '一致',
//   '，都',
//   '强调',
//   '了U',
//   'I审',
//   '核、',
//   '文案',
//   '优化',
//   '、可',
//   '访问',
//   '性检',
//   '查，',
//   '以及',
//   '提升',
//   '效率',
//   '。</th',
//   'ink>',
// ];

// const parser = new StreamJSONParser(partialData => {
//   console.log("实时更新:", partialData);
// });

// chunks.forEach(chunk => parser.feed(chunk));
