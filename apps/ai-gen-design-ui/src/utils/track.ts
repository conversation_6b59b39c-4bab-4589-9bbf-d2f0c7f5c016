// 文档：https://iwiki.woa.com/p/564086851
import { getUser } from '@/utils';

function removeBrackets(str: string) {
  // 当前系统不支持 json 字符串，需要把字符串前后的花括号去掉才可以录入，否则系统会忽略这次上报
  return str.replace(/(^\{)|(\}$)/g, '');
}

export type TrackData = {
  event?: string;
  domain?: string;
  eventAction?: string;
  eventLabel?: string;
  eventValue?: string | Record<string, any>;
  extraInfo?: string | Record<string, any>;
  eventCategory?: string;
  userId?: string;
  ts?: string;
  version?: string;
  env?: string;
  referer?: string;
  uid?: string;
};
const searchParamsMap: Record<keyof TrackData, string> = {
  event: 't',
  ts: 'ts',
  domain: 'd',
  eventAction: 'ea',
  eventLabel: 'el',
  eventValue: 'ev',
  extraInfo: 'et',
  eventCategory: 'ec',
  userId: 'user_id',
  version: 'v',
  env: 'a',
  referer: 'referer',
  uid: 'uid',
};

const combineUrlSearchParams = (params: TrackData) => {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      const val = typeof value === 'object' ? removeBrackets(JSON.stringify(value)) : value;
      searchParams.set(searchParamsMap[key as keyof TrackData], val);
    }
  });
  return searchParams.toString();
};

class RequestQueue {
  queue: Array<string>;
  constructor() {
    this.queue = [];
  }

  async addRequest(url: string) {
    this.queue.push(url);
    await this.processQueue();
  }

  async processQueue() {
    if (this.queue.length === 0) {
      return;
    }

    const url: string = this.queue.shift() as string;

    try {
      await fetch(url, {
        method: 'GET',
      });
    } catch (error) {
      console.error('Error:', error);
    }
    await this.processQueue();
  }
}

class Track {
  private collect_uri: string = import.meta.env.VITE_TRACE_URL;
  private requestQueueFetch = new RequestQueue();
  private version: string = import.meta.env.VITE_APP_VERSION;

  constructor() {}

  private async url(trackData: TrackData) {
    const searchParams = combineUrlSearchParams(trackData);
    return this.collect_uri + '&' + searchParams;
  }

  private async getExtraInfo(user: { userId: string; userName: string }) {
    return {
      version: this.version,
      figmaUserName: user?.userName || '',
    };
  }

  private getEventCategoryByPlatform(eventCategory?: string) {
    if (!eventCategory) {
      return undefined;
    }
    if (eventCategory.indexOf('figma') < 0) {
      return `figma.${eventCategory}`;
    }
    return eventCategory;
  }

  private getEventActionByPlatform(eventAction?: string) {
    if (!eventAction) {
      return undefined;
    }
    if (eventAction.indexOf('figma') < 0) {
      return `figma.${eventAction}`;
    }
    return eventAction;
  }

  private async combineInitTrackData() {
    const user = await getUser();
    return {
      event: 'event',
      ts: new Date().getTime() + '',
      domain: import.meta.env.VITE_TRACK_DOMAIN,
      version: this.version,
      referer: navigator.userAgent,
      env: import.meta.env.VITE_TRACK_ENV,
      userId: user.userId,
      extraInfo: await this.getExtraInfo(user),
    };
  }

  /** 上报 pv/uv */
  async sendPV(router: string = '') {
    const event = 'pageview';
    const collectUrl = await this.url({
      ...(await this.combineInitTrackData()),
      event,
      eventValue: router,
    });
    if (collectUrl) this.requestQueueFetch.addRequest(collectUrl);
  }

  /**
   * 主动上报数据
   * track.send('main.go-next-step'); // 用于记录点击次数
   * track.send("menu.generate", { eventLabel: "label.sum.generate" }); // label 可以用于两级标识，当这个点击行为需要区分不同的情况时，可以加这个
   * track.send('create.failed', { eventValue: JSON.stringify(error) }); // eventValue 用于添加一些额外的信息，比如错误信息
   */
  async send(action: string, trackData?: TrackData) {
    const collectUrl = await this.url({
      ...(await this.combineInitTrackData()),
      ...trackData,
      eventCategory: this.getEventCategoryByPlatform(trackData?.eventCategory),
      eventAction: this.getEventActionByPlatform(action),
    });
    if (collectUrl) this.requestQueueFetch.addRequest(collectUrl);
  }
}

const track = new Track();

export default track;
