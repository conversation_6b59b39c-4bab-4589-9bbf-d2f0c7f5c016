import { MessagePlugin } from 'tdesign-react';

import { MESSAGE_PLUGIN_CONFIG } from '@/constant';

export const createFileElementUpload = (
  onChange: (files: File[]) => void,
  options: {
    accept?: string;
    multiple?: boolean;
    /**
     * 文件最大尺寸，默认 5242880（5MB）
     */
    maxSize?: number;
    /**
     * 文件大小超过限制时的提示信息
     */
    limitExceededMessage?: string;
  } = {},
) => {
  const fileElement = document.createElement('input');
  fileElement.type = 'file';
  fileElement.accept = options.accept ?? '*';
  fileElement.multiple = options.multiple || false;
  fileElement.hidden = true;
  const maxSize = options.maxSize || 5242880;

  fileElement.onchange = () => {
    if (!fileElement.files) return;
    const files = Array.from(fileElement.files);

    if (files.some(file => file.size > maxSize)) {
      MessagePlugin.warning({
        content: options.limitExceededMessage || '文件大小不能超过5MB',
        ...MESSAGE_PLUGIN_CONFIG,
      });
      return;
    }
    onChange(files);
  };
  fileElement.click();

  return () => {
    fileElement.onchange = null;
    if (fileElement.parentElement) {
      fileElement.parentElement.removeChild(fileElement);
    }
  };
};

export const convertFileToBase64 = (file: File) => {
  return new Promise<string | null>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const base64String = reader.result as string;
      resolve(base64String);
    };
    reader.onerror = () => {
      reject(null);
    };
  });
};
