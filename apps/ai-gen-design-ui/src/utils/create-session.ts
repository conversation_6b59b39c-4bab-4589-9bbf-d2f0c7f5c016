import { MessagePlugin } from 'tdesign-react';

import { AI_GEN_DESIGN_URL_PREFIX, MESSAGE_PLUGIN_CONFIG } from '@/constant';

import getUser from './get-user';
import { request } from './request';

export async function createSession() {
  const res = await request(`${AI_GEN_DESIGN_URL_PREFIX}/create-session`, {
    method: 'GET',
    headers: {
      'X-User-Id': (await getUser()).userId,
    },
  });
  const resObj = await res.json();
  const sessionId = resObj.data?.id;
  const taskId = resObj.data?.taskId;
  if (sessionId && taskId) {
    return { sessionId, taskId };
  } else {
    MessagePlugin.error({ content: '创建会话失败', ...MESSAGE_PLUGIN_CONFIG });
    return null;
  }
}
