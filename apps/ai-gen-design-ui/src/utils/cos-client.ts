import COS from 'cos-js-sdk-v5';

import { request } from './request';

interface CosTokens {
  secret_id: string;
  secret_key: string;
  token: string;
  enabled_at: number;
  expired_at: number;
  domain: string;
  bucket: string;
  region: string;
  prefix: string;
  timestamp_ms: number; // 创建时间
  timeDeviation: number; // 计算本地时间与服务器时间的差值
}
export interface UploadOptions {
  file: File;
  params?: Omit<COS.UploadFileParams, 'Body' | 'Key' | 'Region' | 'Bucket'>;
}

let promise: Promise<CosTokens> | null = null;

export class CosClient {
  private cosTokens: CosTokens | null = null;

  constructor() {}

  async getTokensApi(): Promise<CosTokens> {
    const res = await request(`/cos/sts-tokens`, { method: 'GET' });
    const jsonRes = await res.json();
    return jsonRes.data;
  }

  /**
   * 判断token是否过期
   */
  async getIsTokenInvalid(tokens: CosTokens | null): Promise<boolean> {
    if (!tokens) {
      return true;
    }
    const srvDate = Date.now() - (tokens.timeDeviation || 0);
    const expiration = tokens.expired_at * 1000; // 过期时间
    return srvDate > expiration - 60 * 1000; // 减一分钟避免时间误差
  }

  getCosTokens(): Promise<CosTokens> {
    if (promise) {
      return promise;
    }
    promise = new Promise((resolve, reject) => {
      setTimeout(async () => {
        //  tokens不存在/过期了 就重新获取 tokens
        if (await this.getIsTokenInvalid(this.cosTokens)) {
          try {
            this.cosTokens = await this.getTokensApi();
            this.cosTokens.timeDeviation = Date.now() - this.cosTokens.timestamp_ms; // 计算本地时间与服务端时间的差值
          } catch (error) {
            reject(error);
          }
        }
        promise = null;
        resolve(this.cosTokens as CosTokens);
      }, 0);
    });
    return promise;
  }

  async getCos() {
    const tokens = await this.getCosTokens();
    const { secret_id, secret_key, token, enabled_at, expired_at, domain } = tokens;
    return new COS({
      Domain: domain,
      Protocol: 'https',
      getAuthorization(options, callback) {
        callback({
          TmpSecretId: secret_id,
          TmpSecretKey: secret_key,
          SecurityToken: token,
          StartTime: enabled_at, // 时间戳，单位秒，如：1580000000
          ExpiredTime: expired_at, // 时间戳，单位秒，如：1580000000
        });
      },
    });
  }

  async uploadFile({ file, params }: UploadOptions) {
    if (!file) {
      throw new Error('文件不能为空');
    }
    const sliceSize = Math.min(Math.max(5 * 1024 * 1024, file.size / 500), 20 * 1024 * 1024); // 最小 5M，最大 20M

    const filename = file.name;
    const ext = filename.split('.').pop();
    const randomFilename = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${ext}`;
    const tokens = await this.getCosTokens();
    const filePath = `${tokens.prefix}/${randomFilename}`;

    const currCos = await this.getCos();
    await currCos.uploadFile({
      Bucket: tokens.bucket,
      Region: tokens.region,
      SliceSize: sliceSize, // 分片大小
      ...params,
      Key: filePath,
      Body: file,
    });

    const cdnSignedUrl = await request(`/cos/cdn-sign-url`, {
      method: 'POST',
      body: {
        filePath: [filePath],
      },
    });
    const cdnSignedUrlJson = (await cdnSignedUrl.json()).data;
    return {
      url: cdnSignedUrlJson.signedUrls[0] as string,
    };
  }
}

export const cosClient = new CosClient();
