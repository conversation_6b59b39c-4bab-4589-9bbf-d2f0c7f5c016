import {
  DeepPartial,
  DesignComponentsSchema,
  PageArchitectureWithComponentSchema,
  PageWithComponentSchema,
  SectionWithComponentSchema,
  TemplateHtmlListSchema,
} from '@tencent/design-ai-utils';
import { z } from 'zod';

import { Locales } from './locale';

export enum PageType {
  app = 'app',
  web = 'web',
  console = 'console',
}

export enum ReferImageContent {
  structure = 'structure',
  style = 'style',
  copywriting = 'copywriting',
}

export type Section = DeepPartial<z.infer<typeof SectionWithComponentSchema>>;
export type Page = DeepPartial<z.infer<typeof PageWithComponentSchema>>;
export type PageArchitecture = DeepPartial<z.infer<typeof PageArchitectureWithComponentSchema>>;
export type DesignComponents = z.infer<typeof DesignComponentsSchema>;
export type TemplateHtmlList = z.infer<typeof TemplateHtmlListSchema>;

type ContextActive = {
  active: boolean;
};
export interface ArchitectureContext {
  designComponent?: {
    data: DesignComponents;
  } & ContextActive;
  image?: {
    data: string;
    reference: Record<ReferImageContent, boolean>;
    customizeRequirement?: string;
  } & ContextActive;
}

export interface GenerateCodeContext {
  designComponent?: {
    data: any[];
  } & ContextActive;
  templateHtml?: {
    data: TemplateHtmlList;
  } & ContextActive;
}

export interface RequestPageArchitectureBody {
  prompt?: string;
  pageType?: PageType;
  sessionId?: string;
  updateMeasure?: 'whole' | 'page' | 'section';
  updateId?: string;
  pageArchitecture?: PageArchitecture;
  designStylePrompt?: string;
  designStyleKeywords?: string[];
  user?: string;
  context?: ArchitectureContext;
  openInternalImage?: boolean;
}

export type DesignStyleKeyword = {
  key: string;
  value: Record<Locales, string>;
};

export type GeneratePageCode = {
  success: boolean | null;
  pageName: string;
  pageId: string;
  codeId?: string;
  code?: string;
  error?: string;
};

export type RoutePath = 'main' | 'result' | 'detect-demo';
