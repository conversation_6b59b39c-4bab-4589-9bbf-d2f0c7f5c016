import { mountStoreDevtool } from 'simple-zustand-devtools';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

import { DesignComponents, ReferImageContent, TemplateHtmlList } from '@/type';

export interface ContextStore {
  referImage: {
    /** 是否开启参考图片模式 */
    active: boolean;
    /** 参考图片数据，cos url */
    data?: string;
    /** 参考图片的参考范围：页面结构、视觉风格、文本内容三种 */
    reference: Record<ReferImageContent, boolean>;
    /** 参考图片的预览数据，base64格式 */
    previewBase64?: string;
    /** 是否正在上传参考图片 */
    uploading?: boolean;
    /** 自定义垫图要求 */
    customizeRequirement?: string;
  };
  designComponent: {
    /** 是否开启设计组件生成模式 */
    active: boolean;
    /** 设计组件数据 */
    data?: DesignComponents;
    /** 记录设计组件的选中状态 */
    designComponentMap: Record<string, boolean>;
  };
  nodeHtml: {
    /** 是否开启基于选中节点生成 */
    active: boolean;
    /** 基于选中节点生成的html数据 */
    data?: TemplateHtmlList;
  };
}

const useContextStore = create<ContextStore>()(
  immer((): ContextStore => {
    return {
      designComponent: {
        active: false,
        data: undefined,
        designComponentMap: {},
      },
      referImage: {
        active: false,
        reference: {
          structure: true,
          style: true,
          copywriting: false,
        },
      },
      nodeHtml: {
        active: false,
      },
    };
  }),
);

if (import.meta.env.DEV) {
  mountStoreDevtool('ContextStore', useContextStore);
}

export { useContextStore };
