import { mountStoreDevtool } from 'simple-zustand-devtools';
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { DesignStyleKeyword, PageArchitecture, PageType } from '@/type';

export interface AIStore {
  /** 用户初次输入的设计需求，首页聊天框的prompt */
  prompt: string;
  designStyleKeywords: DesignStyleKeyword[];
  designStylePrompt: string;
  pageType: PageType;
  pageArchitecture: PageArchitecture;
  /** 记录页面选择状态 */
  pageSelectMap: Record<string, boolean>;
  /** 当前会话 Id */
  sessionId?: string;
  /** 是否开启隐式垫图 */
  openInternalImage?: boolean;
}

const useAIStore = create<AIStore>()(
  subscribeWithSelector(
    immer((): AIStore => {
      return {
        prompt: '',
        pageType: PageType.app,
        pageArchitecture: {},
        pageSelectMap: {},
        designStyleKeywords: [],
        designStylePrompt: '',
        openInternalImage: false
      };
    }),
  ),
);

// useAIStore.subscribe(
//   (newState) => {
//     return newState.prompt;
//   },
//   (prompt) => {
//     window.localStorage.setItem('$ai-gen-design-prompt', prompt);
//   },
// );

if (import.meta.env.DEV) {
  mountStoreDevtool('AIStore', useAIStore);
}

export { useAIStore };
