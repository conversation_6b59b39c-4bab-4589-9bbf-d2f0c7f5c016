import { MessageTypes } from '@ai-assitant/ai-core';
import { useCallback, useRef, useState } from 'react';

import { figmaMessages } from '@/utils';
import aiEngine from '@/utils/ai-engine';

export default function useDetect() {
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  // 各个页面设计稿通过 ai 识别出来的位置信息
  const [predictionsList, setPredictionsList] = useState<any[]>([]);
  // 各个位置信息转换出来的组件图层信息
  const [componentNodesList, setComponentNodesList] = useState<any[]>([]);
  // 通过图层信息转换出来的 html 信息
  const [html, setHtml] = useState<any>('');
  const globalSettingRef = useRef({
    preview: false,
  });

  // 获取 ai 识别出来的位置信息
  const getPredictions = useCallback(async () => {
    try {
      const designNodesScreenshot = (
        (await figmaMessages[MessageTypes.GET_DESIGN_NODES_SCREEN_SHOT].request({})) as any
      )?.data;
      if (!designNodesScreenshot?.length) {
        setPredictionsList([]);
        return [];
      }
      console.log('designNodesScreenshot', designNodesScreenshot);
      const predictionsList = [] as any;
      // ai 引擎限制了同时最多 2个请求，因此需要拆分
      const step = 2;
      for (let i = 0; i < designNodesScreenshot.length; i = i + step) {
        const arr = designNodesScreenshot.slice(i, i + step);
        predictionsList.push(
          ...(await Promise.all(
            arr.map(async (item: any) => {
              const predictions = await aiEngine.detectFrame({
                type: 'button',
                url: item.screenshot,
              });
              return {
                ...item,
                predictions,
              };
            }),
          )),
        );
      }
      console.log('predictionsList', predictionsList);
      setPredictionsList(predictionsList);
      return predictionsList;
    } catch (error) {
      console.error('getPredictions error', error);
      return [];
    }
  }, []);

  // 获取组件图层信息
  const getComponentNodesList = useCallback(async (preview?: boolean) => {
    if (preview !== undefined) {
      globalSettingRef.current.preview = preview;
    }

    const predictionsList = await getPredictions();
    const componentNodesList = (
      (await figmaMessages[MessageTypes.GET_COMPONENT_NODES_BY_AI_RESULTS].request({
        predictionsList,
        preview: globalSettingRef.current.preview,
      })) as any
    )?.data;
    setComponentNodesList(componentNodesList);
    return componentNodesList;
  }, []);

  // 获取最终组件图层对应的 html 信息
  const getHtml = useCallback(async (preview?: boolean) => {
    if (preview !== undefined) {
      globalSettingRef.current.preview = preview;
    }

    const componentNodesList = await getComponentNodesList();
    const html = (
      (await figmaMessages[MessageTypes.GET_HTML_FROM_COMPONENT_NODES].request({ componentNodesList })) as any
    )?.data;
    setHtml(html);
  }, []);

  return {
    isLoading,
    setIsLoading,
    predictionsList,
    componentNodesList,
    html,
    getPredictions,
    getComponentNodesList,
    getHtml,
  };
}
