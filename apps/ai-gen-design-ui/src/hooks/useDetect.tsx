import { MessageTypes } from '@ai-assitant/ai-core';
import { useCallback, useRef, useState } from 'react';

import { figmaMessages } from '@/utils';
import aiEngine from '@/utils/ai-engine';

export default function useDetect() {
  // 加载状态
  const [isLoading, setIsLoading] = useState(false);
  const globalSettingRef = useRef({
    preview: false,
  });

  // 获取 ai 识别出来的位置信息
  const getPredictions = useCallback(async () => {
    try {
      const designNodesScreenshot = (
        (await figmaMessages[MessageTypes.GET_DESIGN_NODES_SCREEN_SHOT].request({})) as any
      )?.data;
      if (!designNodesScreenshot?.length) {
        return [];
      }
      const predictionsList = [] as any;
      // ai 引擎限制了同时最多 2个请求，因此需要拆分
      const step = 2;
      for (let i = 0; i < designNodesScreenshot.length; i = i + step) {
        const arr = designNodesScreenshot.slice(i, i + step);
        predictionsList.push(
          ...(await Promise.all(
            arr.map(async (item: any) => {
              const predictions = await aiEngine.detectFrame({
                type: 'button',
                url: item.screenshot,
              });
              return {
                ...item,
                predictions,
              };
            }),
          )),
        );
      }
      return predictionsList;
    } catch (error) {
      console.error('getPredictions error', error);
      return [];
    }
  }, []);

  // 获取组件图层信息
  const getComponentNodesList = useCallback(async (preview?: boolean) => {
    if (preview !== undefined) {
      globalSettingRef.current.preview = preview;
    }

    const predictionsList = await getPredictions();
    const componentNodesList = (
      (await figmaMessages[MessageTypes.GET_COMPONENT_NODES_BY_AI_RESULTS].request({
        predictionsList,
        preview: globalSettingRef.current.preview,
      })) as any
    )?.data;
    return componentNodesList;
  }, []);

  // 获取最终组件图层对应的 html 信息
  const getHtml = useCallback(async (preview?: boolean) => {
    if (preview !== undefined) {
      globalSettingRef.current.preview = preview;
    }

    const componentNodesList = await getComponentNodesList();
    const html = (
      (await figmaMessages[MessageTypes.GET_HTML_FROM_COMPONENT_NODES].request({ componentNodesList })) as any
    )?.data;
    return html;
  }, []);

  return {
    isLoading,
    setIsLoading,
    getPredictions,
    getComponentNodesList,
    getHtml,
  };
}
