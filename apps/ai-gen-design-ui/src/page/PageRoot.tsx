import 'tdesign-react/esm/style/index.js';
import '@/style/global.less';

import i18next from 'i18next';
import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MessagePlugin, NotificationInstance, NotificationPlugin } from 'tdesign-react';

import { ResizeWindow } from '@/components';
import { AI_GEN_DESIGN_URL_PREFIX, MESSAGE_PLUGIN_CONFIG, TRACK_ACTION } from '@/constant';
import { Locales } from '@/locale';
import { useAIStore, useContextStore } from '@/store';
import { Page, RequestPageArchitectureBody, RoutePath } from '@/type';
import { filterDesignComponent, sseRequest, StreamJSONParser, track } from '@/utils';

import DetectDemoPage from './detect-demo';
import MainPage from './main';
import ResultPage from './result';

// /** 是否要参考图片 */
// const whetherToReferImage = (isReferImage: AIStore['isReferImage'], referenceImage: AIStore['referenceImage']) => {
//   if (!isReferImage || !referenceImage) return false;
//   // 如果三个参考内容全都是 false，则表示不参考图片
//   const hasNoReferContent = Object.keys(isReferImage).every((key) => !isReferImage[key as ReferImageContent]);
//   return !hasNoReferContent;
// };

function onJsonParse(result: any, updateMeasure?: RequestPageArchitectureBody['updateMeasure'], updateId?: string) {
  useAIStore.setState((draft) => {
    if (updateMeasure === 'page') {
      for (const page of draft.pageArchitecture.pages ?? []) {
        if (page.id === updateId) {
          page.n = result?.n ?? '';
          page.sections = Array.isArray(result?.sections) ? result?.sections : [];
          break;
        }
      }
    } else if (updateMeasure === 'section') {
      outer: for (const page of draft.pageArchitecture.pages ?? []) {
        for (const section of page.sections ?? []) {
          if (section.id === updateId) {
            section.n = result?.n ?? '';
            section.d = result?.d ?? '';
            break outer;
          }
        }
      }
    } else {
      draft.pageArchitecture = result;
      draft.pageSelectMap =
        result?.pages
          ?.map((p: Page) => p.id)
          .filter((id: string) => id !== undefined)
          .reduce(
            (acc: Record<string, boolean>, id: string) => {
              acc[id] = true;
              return acc;
            },
            {} as Record<string, boolean>,
          ) ?? {};
    }
  });
}

export default function PageRoot() {
  const abortPageArchitectureRef = useRef<AbortController | null>(null);
  const notificationInsRef = useRef<NotificationInstance[]>([]);

  const [currentRoute, setCurrentRoute] = useState<RoutePath>('main');
  const [requestPALoading, setRequestPALoading] = useState(false);

  const { t } = useTranslation();

  const navigateTo = useCallback((path: RoutePath) => {
    // 关闭所有通知弹窗
    notificationInsRef.current.forEach((ins) => {
      ins.close();
    });
    notificationInsRef.current = [];
    setCurrentRoute(path);

    if (path === 'main') {
      abortPageArchitectureRequest();
      useAIStore.setState({
        pageArchitecture: {},
        pageSelectMap: {},
        sessionId: undefined,
      });
    }
  }, []);

  const requestPageArchitecture = useCallback(async (payload: RequestPageArchitectureBody = {}) => {
    abortPageArchitectureRequest();
    setRequestPALoading(true);

    const aiState = useAIStore.getState();
    const contextState = useContextStore.getState();
    const { updateMeasure, updateId } = payload;

    // 局部更新时，需要提供 updateId
    if (updateMeasure && updateMeasure !== 'whole' && !updateId) {
      MessagePlugin.error({ content: '当前模式不允许局部更改', ...MESSAGE_PLUGIN_CONFIG });
      return;
    }

    const parserJSON = new StreamJSONParser((result) => {
      onJsonParse(result, updateMeasure, updateId);
    });

    const requestBody: RequestPageArchitectureBody = {
      sessionId: aiState.sessionId,
      prompt: aiState.prompt,
      pageType: aiState.pageType,
      pageArchitecture: aiState.pageArchitecture,
      designStylePrompt: aiState.designStylePrompt,
      designStyleKeywords: aiState.designStyleKeywords.map((keyword) => keyword.value[i18next.language as Locales]),
      openInternalImage: aiState.openInternalImage,
      ...payload,
      updateMeasure,
      context: {
        designComponent: {
          active: contextState.designComponent.active,
          data: filterDesignComponent(
            contextState.designComponent.data,
            contextState.designComponent.designComponentMap,
          ),
        },
        image: {
          active: contextState.referImage.active,
          data: contextState.referImage.data ?? '',
          reference: contextState.referImage.reference,
          customizeRequirement: contextState.referImage.customizeRequirement,
        },
      },
    };

    abortPageArchitectureRef.current = new AbortController();
    return sseRequest(`${AI_GEN_DESIGN_URL_PREFIX}/architecture`, {
      method: 'POST',
      body: requestBody,
      signal: abortPageArchitectureRef.current?.signal,
      onMessage: (msg) => {
        if (msg.event === 'data') {
          parserJSON.feed(msg.data);
        } else if (msg.event === 'session-id' && msg.data !== aiState.sessionId) {
          useAIStore.setState({ sessionId: msg.data });
        }
      },
      onClose: () => {
        parserJSON.reset();
      },
      onError: async (error) => {
        const ins = await NotificationPlugin.error({
          title: t('generate.failed'),
          content: (
            <>
              <p>sessionId: {useAIStore.getState().sessionId}</p>
              <p>{typeof error === 'string' ? error : error.message}</p>
            </>
          ),
          duration: 0,
          closeBtn: true,
          onCloseBtnClick: () => {
            notificationInsRef.current.splice(notificationInsRef.current.indexOf(ins), 1);
          },
        });
        notificationInsRef.current.push(ins);
        track.send(TRACK_ACTION['request-page-architecture-error'], {
          eventValue: {
            error: error.message,
          },
        });
      },
    }).finally(() => {
      setRequestPALoading(false);
      abortPageArchitectureRef.current = null;
    });
  }, []);

  const abortPageArchitectureRequest = useCallback(() => {
    if (abortPageArchitectureRef.current) {
      track.send(TRACK_ACTION['abort-request-page-architecture']);
      abortPageArchitectureRef.current.abort();
      abortPageArchitectureRef.current = null;
    }

    setRequestPALoading(false);
  }, []);

  const renderPage = () => {
    switch (currentRoute) {
      case 'main':
        track.sendPV('main');
        return <MainPage navigateTo={navigateTo} requestPageArchitecture={requestPageArchitecture} />;
      case 'result':
        return (
          <ResultPage
            navigateTo={navigateTo}
            requestPageArchitecture={requestPageArchitecture}
            abortPageArchitectureRequest={abortPageArchitectureRequest}
            requestPALoading={requestPALoading}
          />
        );
      case 'detect-demo':
        return <DetectDemoPage />;
    }
  };

  return (
    <>
      {renderPage()}
      <ResizeWindow />
    </>
  );
}
