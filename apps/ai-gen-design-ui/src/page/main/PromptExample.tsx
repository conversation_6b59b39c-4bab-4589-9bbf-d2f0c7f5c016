import { AnimatePresence, motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';

import { SvgIcon } from '@/components';
import { PromptExamples } from '@/constant';
import { Locales } from '@/locale';
import { PageType } from '@/type';

export default function PromptExample({
  pageType,
  onSelect,
}: {
  pageType: PageType;
  onSelect?: (selectPrompt: string) => void;
}) {
  const { t, i18n } = useTranslation();

  return (
    <>
      <div className="text-xs text-white/60 mb-4">{t('main-page.select-example')}：</div>
      <div className="grid grid-cols-3 gap-x-5 gap-y-4">
        <AnimatePresence mode="popLayout">
          {PromptExamples.filter((o) => o.pageType === pageType).map((item) => {
            const exampleContent = item.content[i18n.language as Locales];
            return (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3 }}
                className="bg-primaryBg border border-primaryBorder rounded text-white text-xs h-8 px-3 cursor-pointer flex items-center gap-2"
                onClick={() => onSelect?.(exampleContent)}
                key={exampleContent}
              >
                <SvgIcon name="star" className="text-primary flex-shrink-0" />
                <span className="truncate">{exampleContent}</span>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </>
  );
}
