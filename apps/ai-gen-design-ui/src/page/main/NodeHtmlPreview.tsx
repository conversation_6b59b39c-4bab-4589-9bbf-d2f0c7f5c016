import { motion, useAnimation } from 'framer-motion';
import { useEffect, useState } from 'react';

// import { Tabs } from 'tdesign-react';
import { Button, Tabs } from '@/components';
import { useContextStore } from '@/store';
import { cn } from '@/utils';

export default function NodeHtmlPreview({ open, onClose }: { open: boolean; onClose: () => void }) {
  const controls = useAnimation();
  const wrapperControls = useAnimation();
  const { data } = useContextStore((s) => s.nodeHtml);
  const [tabsValue, setTabsValue] = useState(data?.[0]?.name);

  useEffect(() => {
    if (open) {
      setTabsValue(data?.[0]?.name);
      controls.start({ opacity: 1, y: 0 });
      wrapperControls.start({ opacity: 1 });
    } else {
      controls.start({ opacity: 0, y: 100 });
      wrapperControls.start({ opacity: 0 });
    }
  }, [open, controls, wrapperControls]);

  const handleClose = async () => {
    await Promise.all([controls.start({ opacity: 0, y: 100 }), wrapperControls.start({ opacity: 0 })]);
    onClose();
  };

  const onWrapperClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return (
    <motion.div
      className={cn('fixed inset-0 z-50 size-screen bg-black/60', !open && 'hidden')}
      onClick={onWrapperClick}
      initial={{ opacity: 0 }}
      animate={wrapperControls}
    >
      <motion.div
        id="design-component-selector"
        className={cn(
          'absolute left-0 bottom-0 w-full h-[80%] bg-primaryBg rounded-t-lg',
          'after:content-[""] after:absolute after:bottom-[-100px] after:w-full after:h-[100px] after:bg-primaryBg',
        )}
        initial={{ opacity: 0, y: 100 }}
        animate={controls}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 30,
        }}
      >
        {open && (
          <div className="p-4 flex flex-col h-full">
            <div className="flex items-center mb-2 gap-3">
              <Tabs
                className="mb-2 shrink-0"
                value={tabsValue}
                items={
                  data?.map((o) => ({
                    label: o.name,
                    value: o.name,
                  })) ?? []
                }
                onChange={(v) => setTabsValue(v)}
              />
              <Button icon="close" className="ml-auto" onClick={handleClose} border={false}></Button>
            </div>

            <div className="flex-1 overflow-y-auto border border-primaryBorder rounded p-2 flex flex-col gap-3">
              {data
                ?.filter((o) => o.name === tabsValue)
                .map((o) => o.html?.map((h) => <div key={h} dangerouslySetInnerHTML={{ __html: h }}></div>))}
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
