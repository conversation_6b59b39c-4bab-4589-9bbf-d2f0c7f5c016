import { selectionEmitter } from '@ai-assitant/ai-figma';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DialogPlugin, MessagePlugin, Switch, Textarea, Tooltip } from 'tdesign-react';
import { useShallow } from 'zustand/react/shallow';

import logo from '@/assets/logo.png';
import logoFoot from '@/assets/logo-foot.png';
import { Button, EditableDiv, PageLayout, SvgIcon, Tabs } from '@/components';
import { MESSAGE_PLUGIN_CONFIG, PageTypes, TRACK_ACTION } from '@/constant';
import { useDetect, useSuggest } from '@/hooks';
import { Locales } from '@/locale';
import { useAIStore, useContextStore } from '@/store';
import type { PageType, RoutePath } from '@/type';
import {
  convertFileToBase64,
  cosClient,
  createFileElementUpload,
  designComponentsExceedLimit,
  filterDesignComponent,
  getDesignComponents,
  isWhitespaceOnly,
  track,
} from '@/utils';

import DesignComponentPanel from './DesignComponentPanel';
import DesignComponentSelector from './DesignComponentSelector';
// import NodeHtmlPanel from './NodeHtmlPanel';
import NodeHtmlPreview from './NodeHtmlPreview';
import PromptExample from './PromptExample';
import ReferImagePanel from './ReferImagePanel';
import StyleSetting from './StyleSetting';

export default function MainPage({
  navigateTo,
  requestPageArchitecture,
}: {
  navigateTo: (path: RoutePath) => void;
  requestPageArchitecture: () => void;
}) {
  const { t, i18n } = useTranslation();
  const prompt = useAIStore((s) => s.prompt);
  const pageType = useAIStore((s) => s.pageType);
  const openInternalImage = useAIStore((s) => s.openInternalImage);
  const { designComponent, referImage, nodeHtml } = useContextStore(
    useShallow((s) => ({
      designComponent: s.designComponent,
      referImage: s.referImage,
      nodeHtml: s.nodeHtml,
    })),
  );
  const chatInputRef = useRef<HTMLDivElement>(null);

  // 获取设计组件的loading
  const [getDesignComponentLoading, setGetDesignComponentLoading] = useState(false);
  // 是否显示组件选择面板
  const [dcSelectorOpen, setDcSelectorOpen] = useState(false);
  const [nodeHtmlPreviewOpen, setNodeHtmlPreviewOpen] = useState(false);
  const [figmaHasSelection, setFigmaHasSelection] = useState(false);

  useEffect(() => {
    selectionEmitter.on((selection) => {
      setFigmaHasSelection(selection.length > 0);
    });

    return () => {
      selectionEmitter.off();
    };
  }, []);

  const { isLoading, setIsLoading, getHtml } = useDetect();

  const { removeSuggestFormat } = useSuggest({
    inputRef: chatInputRef,
    onApply: (val) => {
      useAIStore.setState({ prompt: val });
    },
    referenceContent: prompt,
    requestBody: { pageType },
  });

  const handleSwitchDesignComponentMode = async () => {
    const newDesignComponentMode = !designComponent.active;
    useContextStore.setState((draft) => {
      draft.designComponent.active = newDesignComponentMode;
    });

    if (newDesignComponentMode) {
      setGetDesignComponentLoading(true);

      try {
        const data = await getDesignComponents();
        MessagePlugin.success({ content: '获取组件数据成功', ...MESSAGE_PLUGIN_CONFIG });
        useContextStore.setState((draft) => {
          draft.designComponent.data = data.components;
          draft.designComponent.designComponentMap = data.components?.reduce(
            (acc, o) => {
              acc[o.id] = true;
              return acc;
            },
            {} as Record<string, boolean>,
          );
        });
        (window as any).$nodePageMap = data.nodePageMap;
        if (designComponentsExceedLimit(data)) {
          setDcSelectorOpen(true);
        }
      } catch {
        MessagePlugin.error({ content: '获取组件数据失败', ...MESSAGE_PLUGIN_CONFIG });
        useContextStore.setState((draft) => {
          draft.designComponent.active = false;
          draft.designComponent.designComponentMap = {};
        });
      } finally {
        setGetDesignComponentLoading(false);
      }
    } else {
      setGetDesignComponentLoading(false);
    }
  };

  const handleUploadImage = () => {
    track.send(TRACK_ACTION['upload-reference-image']);

    const destory = createFileElementUpload(
      async (files) => {
        const base64 = await convertFileToBase64(files[0]);
        if (!base64) return;

        useContextStore.setState((draft) => {
          if (!draft.referImage.reference) {
            draft.referImage.reference = { structure: true, style: true, copywriting: false };
          }
          draft.referImage.previewBase64 = base64;
          draft.referImage.active = true;
          draft.referImage.uploading = true;
        });
        try {
          const uploadResult = await cosClient.uploadFile({
            file: files[0],
            params: {
              onFileFinish: (err) => {
                if (err) {
                  console.error('上传失败:', err);
                }
              },
            },
          });
          useContextStore.setState((draft) => {
            draft.referImage.active = true;
            draft.referImage.previewBase64 = undefined;
            draft.referImage.data = uploadResult.url;
            draft.referImage.uploading = false;
          });

          MessagePlugin.success({ content: '图片上传成功', ...MESSAGE_PLUGIN_CONFIG });
        } catch {
          MessagePlugin.error({ content: '上传失败', ...MESSAGE_PLUGIN_CONFIG });

          useContextStore.setState((draft) => {
            draft.referImage.active = false;
            draft.referImage.previewBase64 = undefined;
            draft.referImage.data = undefined;
            draft.referImage.uploading = false;
          });
        }

        destory();
      },
      {
        accept: 'image/jpeg,image/jpg,image/png',
        limitExceededMessage: t('refer-image.limit-exceeded', { size: 5 }),
      },
    );
  };

  const handleDetectHtml = async () => {
    setIsLoading(true);
    const html = await getHtml();
    console.log('html', html);
    if (!html) {
      MessagePlugin.error({ content: '识别失败', ...MESSAGE_PLUGIN_CONFIG });
    } else {
      const map = new Map();
      html.forEach((node: any) => {
        node.componentNodes?.forEach((o: any) => {
          const minifyHtml = o.html?.replace(/\s+/g, ' ').replace(/\n+/g, '\n');
          if (minifyHtml) {
            const codeList = [...(map.get(o.class) ?? [])];
            codeList.push(minifyHtml);
            map.set(o.class, codeList);
          }
        });
      });
      if (map.size) {
        useContextStore.setState((draft) => {
          draft.nodeHtml.active = true;
          draft.nodeHtml.data = Array.from(map.entries()).map(([key, value]) => ({ name: key, html: value }));
        });
      } else {
        MessagePlugin.warning({ content: '未识别到有效代码', ...MESSAGE_PLUGIN_CONFIG });
      }
    }
    setIsLoading(false);
  };

  const promptIsEmpty = useMemo(() => isWhitespaceOnly(prompt), [prompt]);
  // 选择的组件
  const filteredDesignComponents = useMemo(
    () => filterDesignComponent(designComponent.data, designComponent.designComponentMap),
    [designComponent.data, designComponent.designComponentMap],
  );
  // 组件内容量是否超出限制
  const exceedLimit = useMemo(() => {
    return designComponent.active && designComponentsExceedLimit(filteredDesignComponents);
  }, [designComponent.active, filteredDesignComponents]);

  const goNextStep = () => {
    if (exceedLimit) {
      setDcSelectorOpen(true);
    } else {
      navigateTo('result');
      requestPageArchitecture();
    }
  };

  const handleCustomizeReferImageRequirement = () => {
    const dialogInstance = DialogPlugin({
      header: '自定义垫图要求',
      body: <CustomizeReferImageRequirementTextarea />,
      footer: false,
      onClose: () => {
        dialogInstance.destroy();
      },
    });

    dialogInstance.show();
  };

  return (
    <PageLayout
      title={t('main-page.title')}
      footer={
        <Button
          primary
          autoLoading
          border={false}
          disabled={promptIsEmpty || getDesignComponentLoading}
          className="w-48"
          icon="star"
          onClick={goNextStep}
        >
          {t('main-page.next-step')}
        </Button>
      }
    >
      <div className="text-xs text-white/50 mb-2">{t('main-page.device-select')}</div>
      <Tabs
        className="mb-2 shrink-0"
        value={pageType}
        items={PageTypes.map((o) => ({
          ...o,
          label: o.label[i18n.language as Locales],
        }))}
        onChange={(v) => useAIStore.setState({ pageType: v as PageType })}
      />

      <div className="relative">
        <img src={logo} className="absolute w-16 h-14 right-3 -top-10 z-0" />
        <img src={logoFoot} className="absolute w-16 h-4 right-5 -top-2 z-20" />
        <div className="relative z-10 border border-primaryBorder rounded bg-primaryBg p-3 pb-2">
          {designComponent.active && (
            <DesignComponentPanel onClick={() => setDcSelectorOpen(true)} loading={getDesignComponentLoading} />
          )}
          {referImage.active && <ReferImagePanel />}
          {/* {nodeHtml.active && <NodeHtmlPanel onClick={() => setNodeHtmlPreviewOpen(true)} />} */}
          {/* <div className="text-xs text-white mb-2">
            {t('main-page.press')}
            <span className="border border-white/40 rounded px-2 mx-1">Tab</span> {t('main-page.suggest-tip')}
          </div> */}
          <EditableDiv
            ref={chatInputRef}
            value={prompt}
            className="h-[92px] text-white caret-white"
            onInput={(val) => {
              useAIStore.setState({ prompt: removeSuggestFormat(val) });
            }}
            placeholder={t('main-page.input-placeholder')}
          />

          <div className="my-3 border-white/20 border-t border-dashed" />

          <StyleSetting />

          <div className="flex gap-4 h-9 items-center mt-3">
            {/* <ModelSelect key="model-select" value={model} onSelect={v => useAIStore.setState({ model: v })} /> */}
            <div className="flex items-center gap-2 text-xs text-white/60">
              {t('main-page.design-component')}
              :
              <Switch
                value={designComponent.active}
                onChange={handleSwitchDesignComponentMode}
                className="checked:bg-primary"
              />
            </div>
            <div className="flex items-center gap-2 text-xs text-white/60">
              开启隐式垫图:
              <Switch
                value={openInternalImage}
                onChange={() => {
                  useAIStore.setState({ openInternalImage: !openInternalImage });
                }}
                className="checked:bg-primary"
              />
            </div>
            {/* <Button className="px-3 py-1" disabled={!figmaHasSelection} onClick={handleDetectHtml} loading={isLoading}>
              基于选中节点生成
            </Button> */}
            <Button onClick={handleCustomizeReferImageRequirement}>自定义垫图要求</Button>

            <Tooltip content={t('refer-image.tip')} theme="light" showArrow={false}>
              <SvgIcon
                name="add-image"
                className="w-5 h-5 mr-2 text-white cursor-pointer ml-auto"
                onClick={handleUploadImage}
              />
            </Tooltip>
          </div>
        </div>
      </div>

      <div className="shrink-0 mt-6 mb-5">
        <PromptExample
          pageType={pageType}
          onSelect={(v) => {
            useAIStore.setState({ prompt: v });
            chatInputRef.current?.focus();
          }}
        />
      </div>

      <DesignComponentSelector
        open={dcSelectorOpen}
        onClose={() => setDcSelectorOpen(false)}
        exceedLimit={exceedLimit}
      />
      <NodeHtmlPreview open={nodeHtmlPreviewOpen} onClose={() => setNodeHtmlPreviewOpen(false)} />
    </PageLayout>
  );
}

function CustomizeReferImageRequirementTextarea() {
  const referImage = useContextStore((s) => s.referImage);

  return (
    <Textarea
      value={referImage.customizeRequirement}
      onChange={(val) => {
        useContextStore.setState((draft) => {
          draft.referImage.customizeRequirement = val;
        });
      }}
    />
  );
}
