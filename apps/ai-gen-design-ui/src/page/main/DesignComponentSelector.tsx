import { MessageTypes } from '@ai-assitant/ai-core';
import { useVirtualizer } from '@tanstack/react-virtual';
import { motion, useAnimation } from 'framer-motion';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { SmileIcon, SpeechlessIcon } from 'tdesign-icons-react';

import { Button, SvgIcon } from '@/components';
import { useContextStore } from '@/store';
import { cn, figmaMessages } from '@/utils';

export default function DesignComponentSelector({
  open,
  onClose,
  exceedLimit,
}: {
  open: boolean;
  onClose: () => void;
  exceedLimit: boolean;
}) {
  const controls = useAnimation();
  const wrapperControls = useAnimation();
  const parentRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const { data, designComponentMap } = useContextStore((s) => s.designComponent);

  useEffect(() => {
    if (open) {
      controls.start({ opacity: 1, y: 0 });
      wrapperControls.start({ opacity: 1 });
    } else {
      controls.start({ opacity: 0, y: 100 });
      wrapperControls.start({ opacity: 0 });
    }
  }, [open, controls, wrapperControls]);

  const handleClose = async () => {
    await Promise.all([controls.start({ opacity: 0, y: 100 }), wrapperControls.start({ opacity: 0 })]);
    onClose();
  };

  const onWrapperClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleSelectAll = () => {
    const isAllSelected = data?.every((component) => component.n && designComponentMap[component.id]);

    useContextStore.setState((prev) => {
      const newMap = { ...prev.designComponent.designComponentMap };
      data?.forEach((component) => {
        if (component.n) {
          newMap[component.id] = !isAllSelected;
        }
      });
      return { designComponent: { ...prev.designComponent, designComponentMap: newMap } };
    });
  };

  const validComponents = data?.filter((component) => component.id) || [];
  const virtualizer = useVirtualizer({
    count: Math.ceil(validComponents.length / 2), // 每行两个
    getScrollElement: () => parentRef.current,
    estimateSize: () => 40, // 行高估值
    overscan: 5, // 预渲染行数
  });

  return (
    <motion.div
      className={cn('fixed inset-0 z-50 size-screen bg-black/60', !open && 'hidden')}
      onClick={onWrapperClick}
      initial={{ opacity: 0 }}
      animate={wrapperControls}
    >
      <motion.div
        id="design-component-selector"
        className={cn(
          'absolute left-0 bottom-0 w-full h-[80%] bg-primaryBg rounded-t-lg',
          'after:content-[""] after:absolute after:bottom-[-100px] after:w-full after:h-[100px] after:bg-primaryBg',
        )}
        initial={{ opacity: 0, y: 100 }}
        animate={controls}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 30,
        }}
      >
        {open && (
          <div className="p-4 flex flex-col h-full">
            {/* 标题区域 */}
            <div className="flex items-center mb-2 gap-3">
              <h1 className="text-white text-lg font-bold">{t('dc.title')}</h1>
              <div
                className={cn(
                  'text-xs text-white/50 flex items-center gap-1 ml-auto border border-white/20 rounded-md px-2 py-1',
                  {
                    'text-green-500 border-green-500 bg-green-500/10': !exceedLimit,
                    'text-red-500 border-red-500 bg-red-500/10': exceedLimit,
                  },
                )}
              >
                {exceedLimit ? <SpeechlessIcon /> : <SmileIcon />}
                {t(exceedLimit ? 'dc.exceed' : 'dc.no-exceed')}
              </div>

              <Button icon="close" onClick={handleClose} border={false}></Button>
            </div>
            {/* 提示 */}
            <div className="text-xs text-white/50 mb-2">{t('dc.tip')}</div>

            {/* 全选按钮 */}
            <div className="flex justify-end mb-2">
              <Button className="px-3 py-1" onClick={handleSelectAll}>
                {data?.every((component) => component.n && designComponentMap[component.id])
                  ? t('unselect-all')
                  : t('select-all')}
              </Button>
            </div>

            <div ref={parentRef} className="min-w-0 min-h-0 overflow-auto custom-scrollbar flex-1">
              <div className="relative w-full" style={{ height: `${virtualizer.getTotalSize()}px` }}>
                {virtualizer.getVirtualItems().map((virtualRow) => {
                  // 计算该行对应的两个组件的索引
                  const startIndex = virtualRow.index * 2;

                  return (
                    <div
                      key={virtualRow.key}
                      className="grid grid-cols-2 gap-x-8 gap-y-2 absolute w-full"
                      style={{
                        height: `${virtualRow.size}px`,
                        transform: `translateY(${virtualRow.start}px)`,
                      }}
                    >
                      {/* 渲染该行的两个组件 */}
                      {[0, 1].map((offset) => {
                        const index = startIndex + offset;
                        const component = validComponents[index];

                        if (!component) return null;

                        return (
                          <div
                            key={component.id}
                            onClick={() => {
                              useContextStore.setState((prev) => {
                                prev.designComponent.designComponentMap[component.id] =
                                  !prev.designComponent.designComponentMap[component.id];
                              });
                            }}
                            className="flex items-center gap-2 min-w-0 text-xs h-8 text-white px-2 cursor-pointer"
                          >
                            <SvgIcon
                              name="location"
                              className="w-6 h-6 p-1 text-white/60 hover:bg-white/10 rounded"
                              onClick={(e) => {
                                e.stopPropagation();
                                figmaMessages[MessageTypes.AI_GEN_DESIGN].request({
                                  task: 'locate',
                                  id: component.id,
                                  pageId: (window as any).$nodePageMap[component.id],
                                });
                              }}
                            />
                            <span className="truncate">{component.n}</span>
                            <input
                              type="checkbox"
                              checked={designComponentMap[component.id]}
                              className="w-4 h-4 ml-auto rounded appearance-none bg-primaryBg cursor-pointer border border-white/20 checked:bg-primary"
                            />
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
}
