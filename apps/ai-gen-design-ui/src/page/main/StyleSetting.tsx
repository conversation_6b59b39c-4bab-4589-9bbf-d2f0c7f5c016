import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { EditableDiv, TagGroup } from '@/components';
import { DesignStyleKeywords } from '@/constant';
import { Locales } from '@/locale';
import { useAIStore } from '@/store';

export default function StyleSetting() {
  const designStylePrompt = useAIStore((s) => s.designStylePrompt);
  const designStyleKeywords = useAIStore((s) => s.designStyleKeywords);
  const { t, i18n } = useTranslation();

  const designStyleItems = useMemo(() => {
    return DesignStyleKeywords.map((keyword) => ({
      children: keyword.value[i18n.language as Locales],
      className: 'text-white/60',
      onClick: () => {
        useAIStore.setState((draft) => {
          const index = draft.designStyleKeywords.findIndex((k) => k.key === keyword.key);
          if (index !== -1) {
            draft.designStyleKeywords.splice(index, 1);
          } else {
            draft.designStyleKeywords.push(keyword);
          }
        });
      },
      highlight: designStyleKeywords.some((k) => k.key === keyword.key),
    }));
  }, [designStyleKeywords, i18n.language]);

  return (
    <div>
      {/* <div className="text-xs text-white my-2">{t('main-page.style-title')}</div> */}
      <EditableDiv
        value={designStylePrompt}
        className="h-[40px] text-white caret-white"
        onInput={(val) => useAIStore.setState({ designStylePrompt: val })}
        placeholder={t('main-page.style-placeholder')}
      />
      <div className="mt-2 text-xs text-white flex items-center">
        <span className="text-white/60 mr-2">{t('reference')}: </span>
        <TagGroup items={designStyleItems}></TagGroup>
      </div>
    </div>
  );
}
