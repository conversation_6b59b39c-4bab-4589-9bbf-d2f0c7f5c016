import { motion } from 'framer-motion';
import { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useShallow } from 'zustand/react/shallow';

import { EditableDiv, SendButton } from '@/components';
import { useSuggest } from '@/hooks';
import { useAIStore } from '@/store';
import { RequestPageArchitectureBody, Section } from '@/type';
import { isWhitespaceOnly } from '@/utils';

import Dnd from './Dnd';
import PageCard from './PageCard';

export default function ArchitecturePanel({
  requestPALoading,
  requestPageArchitecture,
  pagesIsEmpty,
  abortPageArchitectureRequest,
}: {
  requestPALoading: boolean;
  requestPageArchitecture: (payload: RequestPageArchitectureBody) => void;
  pagesIsEmpty: boolean;
  abortPageArchitectureRequest: () => void;
}) {
  const { t } = useTranslation();

  const [highlightSectionId, setHighlightSectionId] = useState<string | null>(null);
  const [updatePrompt, setUpdatePrompt] = useState('');

  const chatInputRef = useRef<HTMLDivElement>(null);
  // 要更新的 page 或 section 的id
  const updateIdRef = useRef<string>();

  const { pageType, pageArchitecture, sessionId } = useAIStore(
    useShallow((s) => ({
      pageType: s.pageType,
      pageArchitecture: s.pageArchitecture ?? {},
      sessionId: s.sessionId,
    })),
  );

  const { removeSuggestFormat } = useSuggest({
    inputRef: chatInputRef,
    onApply: (val) => {
      setUpdatePrompt(val);
    },
    referenceContent: updatePrompt,
    requestBody: { pageType, update: 1, pageArchitecture },
  });

  const onUpdatePage = async (pageId: string) => {
    updateIdRef.current = pageId;
    await requestPageArchitecture({
      sessionId,
      updateMeasure: 'page',
      updateId: pageId,
    });
    updateIdRef.current = undefined;
  };
  const onUpdateSection = async (sectionId: string) => {
    updateIdRef.current = sectionId;
    await requestPageArchitecture({
      sessionId,
      updateMeasure: 'section',
      updateId: sectionId,
    });
    updateIdRef.current = undefined;
  };
  const onRefreshArchitecture = async () => {
    await requestPageArchitecture({
      sessionId,
      updateMeasure: 'whole',
      prompt: updatePrompt,
    });
  };

  // 是否局部loading
  const partialLoading = requestPALoading && !!updateIdRef.current;
  const updatePromptIsEmpty = useMemo(() => isWhitespaceOnly(updatePrompt), [updatePrompt]);

  return (
    <>
      <div className="relative custom-scrollbar flex-1 pr-2 pb-2 min-h-0 overflow-y-scroll will-change-scroll">
        <Dnd>
          <div
            className="relative grid gap-3 auto-rows-max min-h-full"
            style={{
              gridTemplateColumns: 'repeat(auto-fill, 240px)',
            }}
          >
            {/* loading时展示蒙层 */}
            {requestPALoading && <div className="absolute left-0 top-0 size-full z-50" />}
            {pagesIsEmpty && requestPALoading && <PageCard style={{ height: 400 }} />}
            {pageArchitecture.pages?.map((page) => {
              return (
                <PageCard
                  key={page.id}
                  page={page}
                  onUpdatePage={onUpdatePage}
                  onUpdateSection={onUpdateSection}
                  onSectionClick={(section: Section) => {
                    setHighlightSectionId(section.id!);
                  }}
                  className={partialLoading && updateIdRef.current === page.id ? 'loading-opacity' : ''}
                  sectionDynamicProps={(section) => {
                    return {
                      className: partialLoading && updateIdRef.current === section.id ? 'loading-opacity' : '',
                      showTryAgain: highlightSectionId === section.id && !requestPALoading,
                      canInput: highlightSectionId === section.id,
                    };
                  }}
                />
              );
            })}
          </div>
        </Dnd>
      </div>

      <motion.div
        className="h-[84px] flex flex-col border border-primaryBorder rounded bg-primaryBg p-3 pb-2"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 40 }}
        transition={{
          type: 'spring',
          stiffness: 200,
          damping: 12,
          mass: 0.8,
          bounce: 0.4,
        }}
      >
        <EditableDiv
          ref={chatInputRef}
          value={updatePrompt}
          className=" text-white caret-white flex-1 min-h-0"
          onInput={(val) => setUpdatePrompt(removeSuggestFormat(val))}
          data-placeholder={t('result-page.input-placeholder')}
          onOnlyEnterDown={() => {
            if (updatePromptIsEmpty) return;
            setUpdatePrompt('');
            onRefreshArchitecture();
          }}
        />
        <div className="h-9 flex items-center">
          <SendButton
            loading={requestPALoading}
            className="ml-auto"
            onSend={() => {
              setUpdatePrompt('');
              onRefreshArchitecture();
            }}
            disabled={requestPALoading || updatePromptIsEmpty}
            onStop={abortPageArchitectureRequest}
          ></SendButton>
        </div>
      </motion.div>
    </>
  );
}
