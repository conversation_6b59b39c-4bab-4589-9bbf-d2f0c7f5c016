import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, CopyButton } from '@/components';
import { AI_GEN_DESIGN_URL_PREFIX, API_PREFIX, TRACK_ACTION } from '@/constant';
import { useAIStore } from '@/store';
import { GeneratePageCode } from '@/type';
import { cn, track } from '@/utils';

import CodeEditor from './CodeEditor';
import PreviewFrame from './PreviewFrame';

export default function ResultPanel({
  finishedTaskNum,
  pageCodes,
  currentPageCode,
  changeCurrentPageCode,
}: {
  finishedTaskNum?: number;
  pageCodes: GeneratePageCode[];
  currentPageCode?: GeneratePageCode;
  changeCurrentPageCode: (pageCode: GeneratePageCode) => void;
}) {
  const { t } = useTranslation();

  const pageType = useAIStore((s) => s.pageType);

  const handleDownloadCode = () => {
    track.send(TRACK_ACTION['click-download-code']);
    const a = document.createElement('a');
    a.href =
      'data:text/plain;charset=utf-8,' +
      encodeURIComponent(currentPageCode?.success ? (currentPageCode?.code ?? '') : '');
    a.download = `${currentPageCode?.pageName}.html`;
    a.click();
  };

  const currentPageCodeEmpty = !currentPageCode?.success || currentPageCode?.code?.length === 0;

  return (
    <div className="size-full flex justify-between">
      <div className="flex flex-col w-[59%] min-w-[420px]">
        <div className="relative flex-1 rounded-sm bg-[#EBEBF0] overflow-hidden">
          {currentPageCode && <PreviewFrame pageCode={currentPageCode} pageType={pageType} />}
        </div>
        <div className="h-9 flex items-end gap-2 overflow-auto custom-scrollbar">
          {pageCodes.map((p, i) => {
            const notReady = p.success == null;
            const isReady = p.success != null;
            const isCurrent = p.pageId === currentPageCode?.pageId;
            const isGenerating = finishedTaskNum === i;
            return (
              <div
                className={cn(
                  'flex justify-center items-center h-7 w-[73px] shrink-0 text-xs relative',
                  'cursor-pointer rounded-sm bg-[#EBEBF0] p-1',
                  {
                    'text-red-500': p.success === false,
                    'bg-primary text-black': isCurrent && isReady,
                    'bg-primaryBg text-white/50 cursor-not-allowed': notReady,
                    'loading-shimmer': isGenerating,
                  },
                )}
                onClick={() => {
                  if (p.success != null) changeCurrentPageCode(p);
                }}
              >
                <span className="truncate">{p.pageName}</span>
              </div>
            );
          })}
        </div>
      </div>
      <div className="w-[40%] h-full min-w-[340px] flex flex-col">
        <div className="w-full flex-1 min-h-0 rounded-sm overflow-hidden">
          {currentPageCode && <CodeEditor pageCode={currentPageCode} />}
        </div>
        <div className="h-9 flex items-end justify-end gap-2">
          <Button
            className="text-primary border-primary"
            onClick={() => {
              track.send(TRACK_ACTION['click-preview-code']);
              window.open(`${API_PREFIX}${AI_GEN_DESIGN_URL_PREFIX}/html/${currentPageCode?.codeId}`);
            }}
            disabled={currentPageCodeEmpty}
          >
            {t('click-preview')}
          </Button>
          <Button className="text-primary border-primary" onClick={handleDownloadCode} disabled={currentPageCodeEmpty}>
            {t('download-code')}
          </Button>
          <CopyButton
            feedback="inner"
            disabled={currentPageCodeEmpty}
            content={currentPageCode?.success ? (currentPageCode?.code ?? '') : ''}
            border
            className="border-primary text-primary"
          />
        </div>
      </div>
    </div>
  );
}
