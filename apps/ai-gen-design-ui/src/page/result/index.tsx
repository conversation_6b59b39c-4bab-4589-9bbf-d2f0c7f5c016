import { useUnmount } from 'ahooks';
import { produce } from 'immer';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { MessagePlugin, NotificationPlugin } from 'tdesign-react';
import { useShallow } from 'zustand/react/shallow';

import { Button, PageLayout } from '@/components';
import { AI_GEN_DESIGN_URL_PREFIX, APP_WIDTH, MESSAGE_PLUGIN_CONFIG, TRACK_ACTION, WEB_WIDTH } from '@/constant';
import { useAIStore, useContextStore } from '@/store';
import { GeneratePageCode, RequestPageArchitectureBody, RoutePath } from '@/type';
import { getDesignComponentsResourceData, getUser, htmlParser, request, track } from '@/utils';

import ArchitecturePanel from './architecture-panel';
import GenerateProgress from './GenerateProgress';
import ResultPanel from './result-panel';

interface ResultPageProps {
  navigateTo: (path: RoutePath) => void;
  requestPageArchitecture: (payload: RequestPageArchitectureBody) => void;
  abortPageArchitectureRequest: () => void;
  requestPALoading: boolean;
}

export default function ResultPage({
  navigateTo,
  requestPageArchitecture,
  abortPageArchitectureRequest,
  requestPALoading,
}: ResultPageProps) {
  const { t } = useTranslation();
  const {
    pageArchitecture = {},
    sessionId,
    pageSelectMap,
    pageType,
  } = useAIStore(
    useShallow((s) => ({
      pageArchitecture: s.pageArchitecture,
      sessionId: s.sessionId,
      pageSelectMap: s.pageSelectMap,
      pageType: s.pageType,
    })),
  );
  const { designComponent, nodeHtml } = useContextStore(
    useShallow((s) => ({
      designComponent: s.designComponent,
      nodeHtml: s.nodeHtml,
    })),
  );

  const [userEmotion, setUserEmotion] = useState<'like' | 'dislike' | null>(null);
  // 已完成生成的页面数量，为false时代表没有在进行生成任务
  const [finishedGenerateTaskNum, setFinishedGenerateTaskNum] = useState<number | false>(false);
  const [pageCodes, setPageCodes] = useState<GeneratePageCode[]>([]);
  const [currentPageCode, setCurrentPageCode] = useState<GeneratePageCode | undefined>(pageCodes[0]);
  const [activeBreadcrumb, setActiveBreadcrumb] = useState<'architecture' | 'result'>('architecture');
  const [isGenerating, setIsGenerating] = useState(false);

  const abortGenerateRef = useRef<AbortController | null>(null);
  // 添加一个标志来控制生成任务是否应该继续
  const shouldContinueTaskRef = useRef<boolean>(true);

  useEffect(() => {
    track.sendPV(`result-${activeBreadcrumb}`);
  }, [activeBreadcrumb]);

  useEffect(() => {
    const firstFinishedPageCode = [...pageCodes].reverse().find((p) => p.success != null);
    if (firstFinishedPageCode) setCurrentPageCode(firstFinishedPageCode);
  }, [pageCodes]);

  const runGenerateSingleTask = async (
    targetPageId: string,
    previousGeneratedPages: Array<{ pageId: string; pageName: string; codeId: string }> = [],
    requestBody?: Record<string, any>,
  ) => {
    const targetPage = pageArchitecture.pages?.find((p) => p.id === targetPageId);
    if (!targetPage) {
      return false;
    }

    abortGenerateRef.current = new AbortController();
    try {
      const response = await request(`${AI_GEN_DESIGN_URL_PREFIX}/generate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        signal: abortGenerateRef.current.signal,
        body: {
          ...requestBody,
          targetPageId,
          previousGeneratedPages,
        },
      });

      if (!response.ok) {
        throw new Error(`请求错误: ${response.status}`);
      }
      const result = (await response.json()).data;
      if (result.success) {
        setPageCodes((prev) => {
          const index = prev.findIndex((p) => p.pageId === targetPageId);
          if (index === -1) return prev;

          return produce(prev, (draft) => {
            draft[index].success = true;
            draft[index].code = result.html;
            draft[index].codeId = result.codeId;
          });
        });

        MessagePlugin.success({ content: `${result.pageName} ${t('success')}`, ...MESSAGE_PLUGIN_CONFIG });
        previousGeneratedPages.push({
          pageId: targetPageId,
          pageName: result.pageName,
          codeId: result.codeId,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error: any) {
      if (error.name === 'AbortError') return;
      setPageCodes((prev) => {
        const index = prev.findIndex((p) => p.pageId === targetPageId);
        if (index === -1) return prev;

        return produce(prev, (draft) => {
          draft[index].success = false;
          draft[index].error = error.message || '生成失败';
        });
      });

      NotificationPlugin.error({
        title: t('generate.failed'),
        content: typeof error === 'string' ? error : error.message,
        closeBtn: true,
      });
    }
  };

  const runGenerateTask = async () => {
    setIsGenerating(true);
    setFinishedGenerateTaskNum(0);
    shouldContinueTaskRef.current = true;
    const selectedPages = pageArchitecture.pages?.filter((p) => pageSelectMap[p.id!]) || [];
    if (selectedPages.length === 0) {
      MessagePlugin.error({ content: t('generate.no-page-selected'), ...MESSAGE_PLUGIN_CONFIG });
      setIsGenerating(false);
      return;
    }

    const newPageCodes = selectedPages.map((p) => ({
      success: null,
      pageName: p.n!,
      pageId: p.id!,
    }));

    setPageCodes(newPageCodes);
    setCurrentPageCode(newPageCodes[0]);

    // 用于存储已成功生成的页面信息，作为后续请求的上下文
    const successfulPages: Array<{ pageId: string; pageName: string; codeId: string }> = [];
    const requestBody: Record<string, any> = {
      user: (await getUser()).userId,
      pageArchitecture,
      pageType,
      sessionId,
      context: {},
    };
    if (designComponent.active) {
      requestBody.context.designComponent = {
        active: designComponent.active,
        data: await getDesignComponentsResourceData(pageArchitecture),
      };
    }
    if (nodeHtml.active && nodeHtml.data?.length) {
      requestBody.context.templateHtml = {
        active: nodeHtml.active,
        data: nodeHtml.data,
      };
    }

    for (let i = 0; i < selectedPages.length; i++) {
      if (!shouldContinueTaskRef.current) {
        break;
      }
      const page = selectedPages[i];
      await runGenerateSingleTask(page.id!, successfulPages, requestBody);
      setFinishedGenerateTaskNum(i + 1);
    }

    setFinishedGenerateTaskNum(false);
    setIsGenerating(false);
  };

  const abortGenerateTask = () => {
    shouldContinueTaskRef.current = false;
    if (abortGenerateRef.current) {
      track.send(TRACK_ACTION['abort-generate-code']);
      abortGenerateRef.current.abort();
      abortGenerateRef.current = null;
    }
    setFinishedGenerateTaskNum(false);
  };

  useUnmount(abortGenerateTask);

  const handleInsertCanvas = async (pages: GeneratePageCode[]) => {
    for (const p of pages) {
      if (p.success && p.code) {
        try {
          await htmlParser.create({
            html: p.code,
            name: `${pageArchitecture.an}-${p?.pageName} (id: ${p?.codeId})`,
            pageParams: {
              viewport: pageType === 'app' ? APP_WIDTH : WEB_WIDTH,
              theme: 'light',
              isUseAutoLayout: false,
            },
          });
        } catch (error: any) {
          track.send(TRACK_ACTION['insert-canvas-error'], {
            eventValue: {
              error: error.message,
            },
          });
        }
      }
    }
  };

  // 当前选中的页面  数组形式
  const selectedPage = useMemo(() => Object.keys(pageSelectMap).filter((key) => pageSelectMap[key]), [pageSelectMap]);
  // 页面是否为空
  const pagesIsEmpty = (pageArchitecture?.pages?.length ?? 0) === 0;
  // 已完成的任务数量
  const filteredFinishedTaskNum = finishedGenerateTaskNum === false ? undefined : finishedGenerateTaskNum;

  const renderFooter = (tipElement: ReactNode) => (
    <div className="w-full flex justify-between items-center">
      {isGenerating ? (
        <GenerateProgress
          totalNum={pageCodes.length}
          finishedNum={filteredFinishedTaskNum || 0}
          className="max-w-[460px]"
        />
      ) : activeBreadcrumb === 'architecture' ? (
        tipElement
      ) : (
        <div className="text-white/60 text-xs flex gap-1">
          {t('total')} <span className="text-primary">{pageCodes.length}</span> {t('item')}，{t('success')}
          <span className="text-primary">{pageCodes.filter((p) => p.success).length}</span> {t('item')}，{t('failed')}
          <span className="text-red-500">{pageCodes.filter((p) => !p.success).length}</span> {t('item')}
        </div>
      )}
      <div className="flex gap-2">
        {activeBreadcrumb === 'result' && (
          <>
            <Button
              primary
              autoLoading
              border={false}
              disabled={!currentPageCode?.success || currentPageCode?.code?.length === 0}
              className="w-36 shrink-0"
              icon="star"
              onClick={() => {
                if (currentPageCode) {
                  track.send(TRACK_ACTION['insert-canvas'], {
                    eventValue: { type: 'single' },
                  });
                  return handleInsertCanvas([currentPageCode]);
                }
              }}
            >
              {t('insert-canvas')}
            </Button>
            <Button
              autoLoading
              border
              disabled={pageCodes.some((p) => p.success == null)}
              className="w-36 shrink-0 text-primary border-primary"
              icon="star"
              onClick={() => {
                track.send(TRACK_ACTION['insert-canvas'], {
                  eventValue: { type: 'all' },
                });
                return handleInsertCanvas(pageCodes);
              }}
            >
              {t('all-insert-canvas')}
            </Button>
          </>
        )}

        {activeBreadcrumb === 'architecture' && (
          <Button
            primary
            loading={isGenerating}
            border={false}
            disabled={requestPALoading || pagesIsEmpty || selectedPage.length === 0}
            className="w-48 shrink-0"
            icon="star"
            onClick={() => {
              setActiveBreadcrumb('result');
              track.send(TRACK_ACTION['start-generate-code'], {
                eventValue: {
                  pageCount: selectedPage.length,
                },
              });
              runGenerateTask();
            }}
          >
            {`${t('result-page.start-generate')}(${selectedPage.length})`}
          </Button>
        )}
      </div>
    </div>
  );

  const breadcrumb = [
    {
      label: t('result-page.architecture'),
      key: 'architecture',
      active: activeBreadcrumb === 'architecture',
      onClick: () => {
        if (activeBreadcrumb !== 'architecture') {
          setActiveBreadcrumb('architecture');
        }
      },
    },
    {
      label: t('result-page.result'),
      key: 'result',
      active: activeBreadcrumb === 'result',
      disabled: pageCodes.length === 0,
      onClick: () => {
        if (activeBreadcrumb !== 'result') {
          setActiveBreadcrumb('result');
        }
      },
    },
  ];

  return (
    <PageLayout
      breadcrumb={breadcrumb}
      onBack={() => navigateTo('main')}
      headerButtons={[
        activeBreadcrumb === 'architecture' && (
          <Button
            border={false}
            disabled={requestPALoading}
            icon="refresh"
            onClick={() => {
              return requestPageArchitecture({
                updateMeasure: 'whole',
                prompt: t('result-page.regenerate'),
              });
            }}
          ></Button>
        ),
        <Button
          border={false}
          icon="thumbs-up"
          className={userEmotion === 'like' ? 'text-primary' : ''}
          onClick={() => {
            setUserEmotion((prev) => {
              if (prev === 'like') return null;
              track.send(TRACK_ACTION['like-generate-result']);
              return 'like';
            });
          }}
        ></Button>,
        <Button
          border={false}
          icon="thumbs-down"
          className={userEmotion === 'dislike' ? 'text-primary' : ''}
          onClick={() => {
            setUserEmotion((prev) => {
              if (prev === 'dislike') return null;
              track.send(TRACK_ACTION['dislike-generate-result']);
              return 'dislike';
            });
          }}
        ></Button>,
      ]}
      bodyClassName="flex flex-col overflow-y-hidden"
      footerRender={renderFooter}
    >
      {activeBreadcrumb === 'architecture' && (
        <ArchitecturePanel
          abortPageArchitectureRequest={abortPageArchitectureRequest}
          pagesIsEmpty={pagesIsEmpty}
          requestPALoading={requestPALoading}
          requestPageArchitecture={requestPageArchitecture}
        />
      )}

      {activeBreadcrumb === 'result' && (
        <ResultPanel
          finishedTaskNum={filteredFinishedTaskNum}
          pageCodes={pageCodes}
          currentPageCode={currentPageCode}
          changeCurrentPageCode={setCurrentPageCode}
        />
      )}
    </PageLayout>
  );
}
