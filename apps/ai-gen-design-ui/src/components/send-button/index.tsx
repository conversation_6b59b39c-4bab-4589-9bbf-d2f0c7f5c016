import { useState } from 'react';
import { StopCircleIcon } from 'tdesign-icons-react';

import { cn } from '@/utils';

import SvgIcon from '../svg-icon';

export default function SendButton({
  onStop,
  onSend,
  disabled = false,
  className,
  loading = false,
}: {
  onStop?: () => void;
  onSend?: () => void;
  disabled?: boolean;
  className?: string;
  loading?: boolean;
}) {
  const [internalLoading, setInternalLoading] = useState(false);
  const handleStop = () => {
    setInternalLoading(false);
    onStop?.();
  };

  const handleSend = async () => {
    if (loading === undefined) setInternalLoading(true);

    await onSend?.();

    if (loading === undefined) setInternalLoading(false);
  };

  const mergedLoading = loading || internalLoading;

  return (
    <div className={className}>
      {mergedLoading
        ? (
            <StopCircleIcon
              name="chat-stop"
              className="cursor-pointer text-primary"
              style={{
                height: '32px',
                width: '32px',
              }}
              onClick={handleStop}
            />
          )
        : (
            <div
              className={cn(
                'rounded-full size-8 flex items-center justify-center',
                disabled ? 'cursor-not-allowed bg-white/40 pointer-events-none' : 'cursor-pointer bg-primary',
              )}
              onClick={handleSend}
            >
              <SvgIcon name="send" className="w-5 h-5 text-white" />
            </div>
          )}
    </div>
  );
}
