import { useMount } from 'ahooks';
import { type CSSProperties, useRef, useState } from 'react';

import { cn } from '@/utils';
interface TabsProps {
  items: { label: string; value: string }[];
  onChange?: (value: string) => void;
  defaultValue?: string;
  value?: string;
  className?: string;
  style?: CSSProperties;
}

export default function Tabs({ items, onChange, defaultValue, value, className = '', style }: TabsProps) {
  const isControlled = value !== undefined;

  const [internalValue, setInternalValue] = useState(defaultValue || items[0]?.value);
  const [sliderStyle, setSliderStyle] = useState({ left: 0, width: 0 });
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const tabItemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const activeTab = isControlled ? value : internalValue;

  useMount(() => {
    const activeIndex = items.findIndex(item => item.value === activeTab);
    changeSliderStyle(activeIndex);
  });

  const changeSliderStyle = (index: number) => {
    const tabElement = tabItemRefs.current[index];
    if (tabElement) {
      const { offsetLeft, offsetWidth } = tabElement;
      setSliderStyle({ left: offsetLeft, width: offsetWidth });
    }
  };

  const handleTabClick = (tabValue: string, index: number) => {
    if (tabValue === activeTab) return;

    if (!isControlled) {
      setInternalValue(tabValue);
    }
    changeSliderStyle(index);
    onChange?.(tabValue);
  };

  return (
    <div
      className={cn('relative h-6 bg-primaryBg border border-primaryBorder rounded w-max', className)}
      ref={tabsContainerRef}
      style={style}
    >
      <div className="flex size-full p-[2px]">
        {items.map((item, index) => (
          <div
            key={item.value}
            ref={el => (tabItemRefs.current[index] = el)}
            onClick={() => handleTabClick(item.value, index)}
            className={cn(
              'z-10 px-4 h-[18px] text-xs flex items-center justify-center cursor-pointer transition-colors rounded',
              activeTab === item.value ? 'text-primaryBg' : 'text-primary',
            )}
          >
            {item.label}
          </div>
        ))}
        <div
          className="absolute top-1/2 -translate-y-1/2 h-[18px] bg-primary rounded transition-all duration-300"
          style={sliderStyle}
        />
      </div>
    </div>
  );
}
