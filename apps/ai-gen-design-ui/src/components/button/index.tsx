import { useState } from 'react';
import { LoadingIcon } from 'tdesign-icons-react';

import { cn } from '@/utils';

import SvgIcon from '../svg-icon';

type ReactButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement>;

export interface ButtonProps {
  disabled?: boolean;
  icon?: React.ReactNode;
  className?: string;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  border?: boolean;
  onClick?: ReactButtonProps['onClick'];
  autoLoading?: boolean;
  loading?: boolean;
  primary?: boolean;
}

export default function Button({
  className,
  disabled,
  children,
  icon,
  border = true,
  autoLoading,
  loading,
  onClick,
  primary,
  ...props
}: ButtonProps & { disabled?: boolean }) {
  const [internalLoading, setInternalLoaing] = useState(loading);
  const realLoading = loading ?? internalLoading;

  const renderIcon = () => {
    if (typeof icon === 'string') {
      return <SvgIcon name={icon} />;
    }
    return icon;
  };

  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    if (realLoading || disabled) return;

    if (autoLoading && loading === undefined) {
      setInternalLoaing(true);
    }
    try {
      await onClick?.(e);
    } finally {
      if (autoLoading && loading === undefined) {
        setInternalLoaing(false);
      }
    }
  };

  return (
    <button
      {...props}
      disabled={disabled || realLoading}
      className={cn(
        'flex justify-center items-center gap-1 py-1 px-2 text-xs cursor-pointer transition-all duration-150 rounded text-white',
        {
          'hover:bg-white/10': !disabled,
          'border border-white/20': border,
          'bg-primary hover:bg-primary/80 text-black disabled:bg-[#595858] disabled:opacity-100 disabled:text-white':
            primary,
        },
        className,
        'disabled:opacity-50 disabled:cursor-not-allowed',
      )}
      onClick={handleClick}
    >
      {realLoading ? <LoadingIcon size="medium" /> : renderIcon()}
      {children}
    </button>
  );
}
