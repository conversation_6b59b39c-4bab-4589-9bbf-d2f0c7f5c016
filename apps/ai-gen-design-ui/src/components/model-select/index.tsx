import './index.less';

import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Dropdown, DropdownOption } from 'tdesign-react';

import { Locales } from '@/locale';
import { cn } from '@/utils';

import { AI_MODELS, type AIModel, DEFAULT_AI_MODEL } from './constant';

export interface ModelSelectProps {
  models?: AIModel[];
  value?: string;
  onSelect?: (model: string) => void;
  className?: string;
}

export default function ModelSelect(props: ModelSelectProps) {
  const { i18n } = useTranslation();
  const { models = AI_MODELS, value, onSelect } = props;

  const [internalValue, setInternalValue] = useState(value ?? DEFAULT_AI_MODEL);

  const modelOptions = useMemo(() => {
    return models.map(v => ({
      content: i18n.language === Locales.EN_US ? v.englishName : v.name,
      value: v.key,
    }));
  }, [i18n.language, models]);

  const onChange = (item: DropdownOption) => {
    const selectedModel = item.value as string;
    if (value === undefined) {
      setInternalValue(selectedModel);
    }
    onSelect?.(selectedModel);
  };

  const realValue = value ?? internalValue;

  const curModelOption = useMemo(() => {
    return models.find(it => it.key === realValue)!;
  }, [realValue, models]);

  return (
    <Dropdown
      options={modelOptions}
      maxColumnWidth={200}
      onClick={onChange}
      trigger="click"
      popupProps={{
        overlayInnerClassName: 'model-select-dropdown',
      }}
    >
      <div
        className={cn(
          'h-7 box-content flex items-center justify-around gap-2 cursor-pointer pl-1 pr-2 text-white border border-primaryBg rounded-full bg-[#292A2E] text-xs',
          props.className,
        )}
      >
        <span className="size-5 rounded-full bg-primary"></span>
        <span>{i18n.language === Locales.EN_US ? curModelOption.englishName : curModelOption.name}</span>
      </div>
    </Dropdown>
  );
}
