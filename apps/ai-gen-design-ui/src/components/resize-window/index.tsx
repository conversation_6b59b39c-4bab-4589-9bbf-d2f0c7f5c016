import { MessageTypes } from '@ai-assitant/ai-core';
import { MouseEventHandler, useEffect, useRef } from 'react';

import { figmaMessages } from '@/utils';

import SvgIcon from '../svg-icon';

export default function ResizeWindow() {
  const draggingRef = useRef(false);
  // 鼠标位置与窗口右下角的距离
  const diff = useRef({ x: 0, y: 0 });

  const handleMouseDown: MouseEventHandler<HTMLDivElement> = (event) => {
    draggingRef.current = true;
    diff.current = {
      x: document.body.scrollWidth - event.pageX,
      y: document.body.scrollHeight - event.pageY,
    };
  };

  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (draggingRef.current) {
        figmaMessages[MessageTypes.RESIZE_TO].send({
          width: Math.max(event.pageX + diff.current.x, 360),
          height: Math.max(event.pageY + diff.current.y, 400),
        });
      }
    };

    const handleMouseUp = () => {
      draggingRef.current = false;
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  return (
    <div onMouseDown={handleMouseDown} className="fixed bottom-0 right-0 z-10 cursor-nwse-resize size-5">
      <SvgIcon name="resize" className="size-full text-white" />
    </div>
  );
}
