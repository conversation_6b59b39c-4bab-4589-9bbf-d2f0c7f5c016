{"name": "@ai-assitant/ai-gen-design-ui", "version": "0.0.1", "description": "", "type": "module", "scripts": {"dev": "vite", "build": "pnpm build:before && pnpm build:figma-plugin && vite build", "build:dev": "pnpm build:before && pnpm build:figma-plugin && vite build --mode development --watch", "build:before": "rm -rf dist && pnpm --filter @tencent/design-ai-utils build", "build:figma-plugin": "pnpm --filter @ai-assitant/ai-figma build && pnpm --filter @ai-assitant/ai-figma build:gd-plugin", "test": "vitest", "tscheck": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"autoprefixer": "^10.4.20", "globals": "^15.9.0", "less": "^4.2.0", "simple-zustand-devtools": "^1.1.0", "tailwindcss": "^3.4.15", "vite-plugin-generate-file": "^0.2.0", "vite-plugin-svg-icons": "^2.0.1"}, "dependencies": {"@ai-assitant/ai-bridge": "workspace:*", "@ai-assitant/ai-core": "workspace:*", "@ai-assitant/ai-figma": "workspace:*", "@tencent/h2d-html-parser": "workspace:*", "@tencent/design-ai-utils": "workspace:*", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "@tanstack/react-virtual": "^3.13.2", "clsx": "^2.1.1", "cos-js-sdk-v5": "^1.8.3", "framer-motion": "^12.4.7", "i18next": "^23.15.2", "immer": "^10.1.1", "inferencejs": "^1.0.21", "react-i18next": "^15.1.1", "swr": "^2.3.3", "tailwind-merge": "2.6.0", "tdesign-icons-react": "^0.4.2", "tdesign-react": "^1.10.0", "zustand": "^5.0.4"}}