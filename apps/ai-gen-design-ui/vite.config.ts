import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig, type UserConfigFnObject } from 'vite';
import generateFile from 'vite-plugin-generate-file';
import { viteSingleFile } from 'vite-plugin-singlefile';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

import figmaManifest from './figma.manifest';

export default defineConfig((({ mode }) => {
  return {
    plugins: [
      react(),
      viteSingleFile(),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/assets')], // svg地址
        symbolId: 'icon-[dir]-[name]',
      }),
      generateFile({
        type: 'json',
        output: './manifest.json',
        data: figmaManifest,
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve('src'),
      },
    },
    build: {
      minify: mode === 'production' ? 'terser' : false,
      terserOptions: {
        format: {
          comments: false,
          beautify: false,
        },
      },
      sourcemap: mode !== 'production' ? 'inline' : false,
      emptyOutDir: false,
      outDir: path.resolve('dist'),
      rollupOptions: {
        input: path.resolve('./index.html'),
      },
    },
    server: {
      port: 4009,
      host: '0.0.0.0',
    },
  };
}) as UserConfigFnObject);
